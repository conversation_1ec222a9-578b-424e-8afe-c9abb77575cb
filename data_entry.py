#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تبويب إدخال البيانات
Data Entry Tab
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import datetime
from ui_components import *
import pandas as pd

class DataEntryTab:
    def __init__(self, parent, db_manager, printer_manager, colors, font):
        self.parent = parent
        self.db_manager = db_manager
        self.printer_manager = printer_manager
        self.colors = colors
        self.font = font
        self.frame = None
        self.current_patient_id = None
        self.create_ui()
        
    def create_ui(self):
        """إنشاء واجهة إدخال البيانات"""
        self.frame = ModernFrame(self.parent, bg=self.colors['surface'])
        
        # عنوان التبويب
        title_label = ModernLabel(
            self.frame,
            text="إدخال بيانات المرضى والعينات",
            font=('Arial', 16, 'bold'),
            fg=self.colors['primary'],
            bg=self.colors['surface']
        )
        title_label.pack(pady=20)
        
        # إنشاء الإطار الرئيسي مع التمرير
        self.create_scrollable_frame()
        
        # إنشاء نموذج إدخال البيانات
        self.create_data_form()
        
        # إنشاء أزرار العمليات
        self.create_action_buttons()
        
        # إنشاء جدول البيانات
        self.create_data_table()
        
        # تحديث البيانات
        self.refresh_data()
        
    def create_scrollable_frame(self):
        """إنشاء إطار قابل للتمرير"""
        # إنشاء Canvas للتمرير
        self.canvas = tk.Canvas(self.frame, bg=self.colors['surface'])
        self.scrollbar = tk.Scrollbar(self.frame, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ModernFrame(self.canvas, bg=self.colors['surface'])
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        
        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")
        
    def create_data_form(self):
        """إنشاء نموذج إدخال البيانات"""
        # إطار النموذج
        form_frame = ModernLabelFrame(
            self.scrollable_frame,
            text="بيانات المريض والعينة",
            bg=self.colors['surface'],
            fg=self.colors['primary']
        )
        form_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # إنشاء الحقول في شبكة
        self.create_form_fields(form_frame)
        
    def create_form_fields(self, parent):
        """إنشاء حقول النموذج"""
        # متغيرات النموذج
        self.form_vars = {}
        
        # الحقول المطلوبة
        fields = [
            ("name", "الاسم *", "entry", True),
            ("age", "العمر *", "entry", True),
            ("gender", "الجنس *", "combobox", True, ["M", "F"]),
            ("address", "العنوان *", "entry", True),
            ("phone", "رقم الهاتف *", "entry", True),
            ("sample_type", "نوع العينة *", "combobox", True, []),
            ("sender_organization", "جهة الإرسال *", "combobox", True, []),
            ("sample_collection_date", "تاريخ سحب العينة *", "date", True),
            ("passport_number", "رقم الجواز", "entry", False),
            ("receipt_number", "رقم الوصل", "entry", False)
        ]
        
        row = 0
        for field_info in fields:
            field_name = field_info[0]
            field_label = field_info[1]
            field_type = field_info[2]
            is_required = field_info[3]
            options = field_info[4] if len(field_info) > 4 else []
            
            # تسمية الحقل
            label = ModernLabel(
                parent,
                text=field_label,
                bg=self.colors['surface'],
                fg=self.colors['text']
            )
            label.grid(row=row, column=0, sticky="e", padx=10, pady=5)
            
            # إنشاء الحقل حسب النوع
            if field_type == "entry":
                var = tk.StringVar()
                widget = ModernEntry(parent, textvariable=var, width=30)
            elif field_type == "combobox":
                var = tk.StringVar()
                widget = ModernCombobox(parent, textvariable=var, width=28)
                if field_name == "sample_type":
                    self.load_sample_types(widget)
                elif field_name == "sender_organization":
                    self.load_sender_organizations(widget)
                else:
                    widget['values'] = options
            elif field_type == "date":
                var = tk.StringVar()
                widget = ModernEntry(parent, textvariable=var, width=30)
                # تعيين التاريخ الحالي كافتراضي
                var.set(datetime.date.today().strftime("%Y-%m-%d"))
                
            widget.grid(row=row, column=1, sticky="w", padx=10, pady=5)
            self.form_vars[field_name] = var
            
            row += 1
            
        # حقول تلقائية (للعرض فقط)
        auto_fields = [
            ("national_id", "الرقم الوطني", ""),
            ("sample_received_date", "تاريخ استلام العينة", datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        ]
        
        for field_name, field_label, default_value in auto_fields:
            label = ModernLabel(
                parent,
                text=field_label,
                bg=self.colors['surface'],
                fg=self.colors['text_secondary']
            )
            label.grid(row=row, column=0, sticky="e", padx=10, pady=5)
            
            var = tk.StringVar(value=default_value)
            widget = ModernEntry(parent, textvariable=var, width=30, state='readonly')
            widget.grid(row=row, column=1, sticky="w", padx=10, pady=5)
            self.form_vars[field_name] = var
            
            row += 1
            
        # إطار التحاليل
        self.create_tests_frame(parent, row)
        
    def create_tests_frame(self, parent, start_row):
        """إنشاء إطار التحاليل"""
        tests_frame = ModernLabelFrame(
            parent,
            text="التحاليل المطلوبة",
            bg=self.colors['surface'],
            fg=self.colors['primary']
        )
        tests_frame.grid(row=start_row, column=0, columnspan=2, sticky="ew", padx=10, pady=10)
        
        # قائمة التحاليل المتاحة
        self.tests_listbox = tk.Listbox(
            tests_frame,
            selectmode=tk.MULTIPLE,
            height=6,
            font=self.font
        )
        self.tests_listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # تحميل التحاليل
        self.load_tests()
        
    def load_sample_types(self, widget):
        """تحميل أنواع العينات"""
        sample_types = self.db_manager.fetch_all("SELECT name FROM sample_types ORDER BY name")
        widget['values'] = [row['name'] for row in sample_types]
        
    def load_sender_organizations(self, widget):
        """تحميل جهات الإرسال"""
        organizations = self.db_manager.fetch_all("SELECT name FROM sender_organizations ORDER BY name")
        widget['values'] = [row['name'] for row in organizations]
        
    def load_tests(self):
        """تحميل التحاليل"""
        tests = self.db_manager.fetch_all("SELECT id, name FROM tests ORDER BY name")
        self.tests_listbox.delete(0, tk.END)
        self.tests_data = {}
        
        for test in tests:
            self.tests_listbox.insert(tk.END, test['name'])
            self.tests_data[test['name']] = test['id']
            
    def create_action_buttons(self):
        """إنشاء أزرار العمليات"""
        buttons_frame = ModernFrame(self.scrollable_frame, bg=self.colors['surface'])
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # زر إضافة
        add_btn = ModernButton(
            buttons_frame,
            text="إضافة",
            command=self.add_patient,
            bg='#28A745',
            fg='white',
            font=self.font
        )
        add_btn.pack(side=tk.LEFT, padx=5)
        
        # زر تعديل
        edit_btn = ModernButton(
            buttons_frame,
            text="تعديل",
            command=self.edit_patient,
            bg='#FFC107',
            fg='black',
            font=self.font
        )
        edit_btn.pack(side=tk.LEFT, padx=5)
        
        # زر حذف
        delete_btn = ModernButton(
            buttons_frame,
            text="حذف",
            command=self.delete_patient,
            bg='#DC3545',
            fg='white',
            font=self.font
        )
        delete_btn.pack(side=tk.LEFT, padx=5)
        
        # زر مسح النموذج
        clear_btn = ModernButton(
            buttons_frame,
            text="مسح النموذج",
            command=self.clear_form,
            bg='#6C757D',
            fg='white',
            font=self.font
        )
        clear_btn.pack(side=tk.LEFT, padx=5)
        
        # زر استيراد Excel
        import_btn = ModernButton(
            buttons_frame,
            text="استيراد Excel",
            command=self.import_excel,
            bg='#17A2B8',
            fg='white',
            font=self.font
        )
        import_btn.pack(side=tk.LEFT, padx=5)
        
        # زر طباعة الستيكر
        print_btn = ModernButton(
            buttons_frame,
            text="طباعة الستيكر",
            command=self.print_sticker,
            bg=self.colors['primary'],
            fg='white',
            font=self.font
        )
        print_btn.pack(side=tk.RIGHT, padx=5)
        
    def create_data_table(self):
        """إنشاء جدول البيانات"""
        table_frame = ModernLabelFrame(
            self.scrollable_frame,
            text="بيانات المرضى",
            bg=self.colors['surface'],
            fg=self.colors['primary']
        )
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # إنشاء الجدول
        columns = ("الرقم الوطني", "الاسم", "العمر", "الجنس", "نوع العينة", "جهة الإرسال", "تاريخ السحب")
        self.tree = ModernTreeview(table_frame, columns=columns, show='headings', height=10)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120, anchor='center')
            
        # شريط التمرير للجدول
        tree_scrollbar = tk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=tree_scrollbar.set)
        
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط حدث النقر على الجدول
        self.tree.bind('<ButtonRelease-1>', self.on_tree_select)
        
        # إطار البحث
        search_frame = SearchFrame(table_frame, self.search_patients)
        search_frame.pack(fill=tk.X, pady=5)

    def validate_form(self):
        """التحقق من صحة البيانات"""
        required_fields = ['name', 'age', 'gender', 'address', 'phone', 'sample_type',
                          'sender_organization', 'sample_collection_date']

        for field in required_fields:
            if not self.form_vars[field].get().strip():
                messagebox.showerror("خطأ", f"يرجى ملء حقل {field}")
                return False

        # التحقق من العمر
        try:
            age = int(self.form_vars['age'].get())
            if age < 0 or age > 150:
                messagebox.showerror("خطأ", "العمر غير صحيح")
                return False
        except ValueError:
            messagebox.showerror("خطأ", "العمر يجب أن يكون رقماً")
            return False

        # التحقق من التاريخ
        try:
            datetime.datetime.strptime(self.form_vars['sample_collection_date'].get(), "%Y-%m-%d")
        except ValueError:
            messagebox.showerror("خطأ", "تاريخ سحب العينة غير صحيح (YYYY-MM-DD)")
            return False

        return True

    def add_patient(self):
        """إضافة مريض جديد"""
        if not self.validate_form():
            return

        try:
            # الحصول على الرقم الوطني التالي
            national_id = self.db_manager.get_next_counter('national_id')
            self.form_vars['national_id'].set(str(national_id))

            # إدراج بيانات المريض
            query = '''
                INSERT INTO patients (
                    national_id, name, age, gender, address, phone, passport_number,
                    receipt_number, sample_type, sender_organization, sample_collection_date
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            '''

            params = (
                national_id,
                self.form_vars['name'].get(),
                int(self.form_vars['age'].get()),
                self.form_vars['gender'].get(),
                self.form_vars['address'].get(),
                self.form_vars['phone'].get(),
                self.form_vars['passport_number'].get() or None,
                self.form_vars['receipt_number'].get() or None,
                self.form_vars['sample_type'].get(),
                self.form_vars['sender_organization'].get(),
                self.form_vars['sample_collection_date'].get()
            )

            cursor = self.db_manager.execute_query(query, params)
            if cursor:
                patient_id = cursor.lastrowid

                # إضافة التحاليل المحددة
                selected_tests = self.tests_listbox.curselection()
                for index in selected_tests:
                    test_name = self.tests_listbox.get(index)
                    test_id = self.tests_data[test_name]

                    test_query = '''
                        INSERT INTO patient_tests (patient_id, test_id)
                        VALUES (?, ?)
                    '''
                    self.db_manager.execute_query(test_query, (patient_id, test_id))

                messagebox.showinfo("نجح", "تم إضافة المريض بنجاح")
                self.refresh_data()
                self.clear_form()

                # طباعة الستيكر تلقائياً
                self.print_sticker_for_patient(patient_id)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إضافة المريض: {str(e)}")

    def edit_patient(self):
        """تعديل بيانات المريض"""
        if not self.current_patient_id:
            messagebox.showwarning("تحذير", "يرجى اختيار مريض للتعديل")
            return

        if not self.validate_form():
            return

        try:
            query = '''
                UPDATE patients SET
                    name = ?, age = ?, gender = ?, address = ?, phone = ?,
                    passport_number = ?, receipt_number = ?, sample_type = ?,
                    sender_organization = ?, sample_collection_date = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            '''

            params = (
                self.form_vars['name'].get(),
                int(self.form_vars['age'].get()),
                self.form_vars['gender'].get(),
                self.form_vars['address'].get(),
                self.form_vars['phone'].get(),
                self.form_vars['passport_number'].get() or None,
                self.form_vars['receipt_number'].get() or None,
                self.form_vars['sample_type'].get(),
                self.form_vars['sender_organization'].get(),
                self.form_vars['sample_collection_date'].get(),
                self.current_patient_id
            )

            self.db_manager.execute_query(query, params)

            # تحديث التحاليل
            # حذف التحاليل القديمة
            self.db_manager.execute_query(
                "DELETE FROM patient_tests WHERE patient_id = ?",
                (self.current_patient_id,)
            )

            # إضافة التحاليل الجديدة
            selected_tests = self.tests_listbox.curselection()
            for index in selected_tests:
                test_name = self.tests_listbox.get(index)
                test_id = self.tests_data[test_name]

                test_query = '''
                    INSERT INTO patient_tests (patient_id, test_id)
                    VALUES (?, ?)
                '''
                self.db_manager.execute_query(test_query, (self.current_patient_id, test_id))

            messagebox.showinfo("نجح", "تم تعديل بيانات المريض بنجاح")
            self.refresh_data()
            self.clear_form()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تعديل المريض: {str(e)}")

    def delete_patient(self):
        """حذف المريض"""
        if not self.current_patient_id:
            messagebox.showwarning("تحذير", "يرجى اختيار مريض للحذف")
            return

        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا المريض؟"):
            try:
                # حذف التحاليل أولاً
                self.db_manager.execute_query(
                    "DELETE FROM patient_tests WHERE patient_id = ?",
                    (self.current_patient_id,)
                )

                # حذف المريض
                self.db_manager.execute_query(
                    "DELETE FROM patients WHERE id = ?",
                    (self.current_patient_id,)
                )

                messagebox.showinfo("نجح", "تم حذف المريض بنجاح")
                self.refresh_data()
                self.clear_form()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء حذف المريض: {str(e)}")

    def clear_form(self):
        """مسح النموذج"""
        for var in self.form_vars.values():
            var.set("")

        # إعادة تعيين التاريخ الحالي
        self.form_vars['sample_received_date'].set(
            datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )

        # مسح اختيار التحاليل
        self.tests_listbox.selection_clear(0, tk.END)

        self.current_patient_id = None

    def refresh_data(self):
        """تحديث البيانات"""
        # مسح الجدول
        for item in self.tree.get_children():
            self.tree.delete(item)

        # جلب البيانات
        query = '''
            SELECT id, national_id, name, age, gender, sample_type,
                   sender_organization, sample_collection_date
            FROM patients
            ORDER BY national_id DESC
        '''

        patients = self.db_manager.fetch_all(query)

        # إدراج البيانات في الجدول
        for patient in patients:
            self.tree.insert('', tk.END, values=(
                patient['national_id'],
                patient['name'],
                patient['age'],
                'ذكر' if patient['gender'] == 'M' else 'أنثى',
                patient['sample_type'],
                patient['sender_organization'],
                patient['sample_collection_date']
            ), tags=(patient['id'],))

    def on_tree_select(self, event):
        """عند اختيار عنصر من الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            patient_id = item['tags'][0]
            self.load_patient_data(patient_id)

    def load_patient_data(self, patient_id):
        """تحميل بيانات المريض"""
        patient = self.db_manager.fetch_one(
            "SELECT * FROM patients WHERE id = ?",
            (patient_id,)
        )

        if patient:
            self.current_patient_id = patient_id

            # تحميل البيانات في النموذج
            self.form_vars['national_id'].set(str(patient['national_id']))
            self.form_vars['name'].set(patient['name'])
            self.form_vars['age'].set(str(patient['age']))
            self.form_vars['gender'].set(patient['gender'])
            self.form_vars['address'].set(patient['address'])
            self.form_vars['phone'].set(patient['phone'])
            self.form_vars['passport_number'].set(patient['passport_number'] or '')
            self.form_vars['receipt_number'].set(patient['receipt_number'] or '')
            self.form_vars['sample_type'].set(patient['sample_type'])
            self.form_vars['sender_organization'].set(patient['sender_organization'])
            self.form_vars['sample_collection_date'].set(patient['sample_collection_date'])
            self.form_vars['sample_received_date'].set(patient['sample_received_date'])

            # تحميل التحاليل
            tests = self.db_manager.fetch_all('''
                SELECT t.name FROM tests t
                JOIN patient_tests pt ON t.id = pt.test_id
                WHERE pt.patient_id = ?
            ''', (patient_id,))

            # مسح الاختيار الحالي
            self.tests_listbox.selection_clear(0, tk.END)

            # اختيار التحاليل
            for test in tests:
                test_name = test['name']
                for i in range(self.tests_listbox.size()):
                    if self.tests_listbox.get(i) == test_name:
                        self.tests_listbox.selection_set(i)
                        break

    def search_patients(self, search_text):
        """البحث في المرضى"""
        # مسح الجدول
        for item in self.tree.get_children():
            self.tree.delete(item)

        if not search_text.strip():
            self.refresh_data()
            return

        # البحث في قاعدة البيانات
        query = '''
            SELECT id, national_id, name, age, gender, sample_type,
                   sender_organization, sample_collection_date
            FROM patients
            WHERE name LIKE ? OR national_id LIKE ? OR phone LIKE ?
            ORDER BY national_id DESC
        '''

        search_pattern = f"%{search_text}%"
        patients = self.db_manager.fetch_all(query, (search_pattern, search_pattern, search_pattern))

        # إدراج النتائج
        for patient in patients:
            self.tree.insert('', tk.END, values=(
                patient['national_id'],
                patient['name'],
                patient['age'],
                'ذكر' if patient['gender'] == 'M' else 'أنثى',
                patient['sample_type'],
                patient['sender_organization'],
                patient['sample_collection_date']
            ), tags=(patient['id'],))

    def import_excel(self):
        """استيراد البيانات من Excel"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف Excel",
            filetypes=[("Excel files", "*.xlsx *.xls")]
        )

        if not file_path:
            return

        try:
            # قراءة ملف Excel
            df = pd.read_excel(file_path)

            # التحقق من الأعمدة المطلوبة
            required_columns = ['name', 'age', 'gender', 'address', 'phone', 'sample_type', 'sender_organization']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                messagebox.showerror("خطأ", f"الأعمدة التالية مفقودة: {', '.join(missing_columns)}")
                return

            # استيراد البيانات
            imported_count = 0
            for index, row in df.iterrows():
                try:
                    # الحصول على الرقم الوطني التالي
                    national_id = self.db_manager.get_next_counter('national_id')

                    # إدراج المريض
                    query = '''
                        INSERT INTO patients (
                            national_id, name, age, gender, address, phone,
                            sample_type, sender_organization, sample_collection_date
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    '''

                    params = (
                        national_id,
                        row['name'],
                        int(row['age']),
                        row['gender'],
                        row['address'],
                        row['phone'],
                        row['sample_type'],
                        row['sender_organization'],
                        datetime.date.today().strftime("%Y-%m-%d")
                    )

                    cursor = self.db_manager.execute_query(query, params)
                    if cursor:
                        patient_id = cursor.lastrowid
                        imported_count += 1

                        # طباعة الستيكر
                        self.print_sticker_for_patient(patient_id)

                except Exception as e:
                    print(f"خطأ في استيراد الصف {index + 1}: {e}")

            messagebox.showinfo("نجح", f"تم استيراد {imported_count} مريض بنجاح")
            self.refresh_data()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء استيراد الملف: {str(e)}")

    def print_sticker(self):
        """طباعة الستيكر للمريض المحدد"""
        if not self.current_patient_id:
            messagebox.showwarning("تحذير", "يرجى اختيار مريض لطباعة الستيكر")
            return

        self.print_sticker_for_patient(self.current_patient_id)

    def print_sticker_for_patient(self, patient_id):
        """طباعة الستيكر لمريض محدد"""
        try:
            # جلب بيانات المريض
            patient = self.db_manager.fetch_one(
                "SELECT * FROM patients WHERE id = ?",
                (patient_id,)
            )

            if patient:
                sticker_data = {
                    'name': patient['name'],
                    'national_id': patient['national_id'],
                    'sample_type': patient['sample_type'],
                    'date': patient['sample_collection_date']
                }

                self.printer_manager.print_sticker(sticker_data)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء طباعة الستيكر: {str(e)}")

    def show(self):
        """عرض التبويب"""
        if self.frame:
            self.frame.pack(fill=tk.BOTH, expand=True)

    def hide(self):
        """إخفاء التبويب"""
        if self.frame:
            self.frame.pack_forget()
