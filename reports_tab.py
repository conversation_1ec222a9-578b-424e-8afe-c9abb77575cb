#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تبويب التقارير والإحصائيات
Reports and Statistics Tab
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import datetime
from ui_components import *
import tempfile
import os

class ReportsTab:
    def __init__(self, parent, db_manager, colors, font):
        self.parent = parent
        self.db_manager = db_manager
        self.colors = colors
        self.font = font
        self.frame = None
        self.create_ui()

    def create_ui(self):
        """إنشاء واجهة التقارير"""
        self.frame = ModernFrame(self.parent, bg=self.colors['surface'])

        # عنوان التبويب
        title_label = ModernLabel(
            self.frame,
            text="التقارير والإحصائيات",
            font=('Arial', 16, 'bold'),
            fg=self.colors['primary'],
            bg=self.colors['surface']
        )
        title_label.pack(pady=20)

        # إنشاء الإطار الرئيسي مع التمرير
        self.create_scrollable_frame()

        # إنشاء منطقة الفلترة
        self.create_filter_section()

        # إنشاء جدول النتائج
        self.create_results_table()

        # إنشاء منطقة الإحصائيات
        self.create_statistics_section()

        # إنشاء أزرار التقارير
        self.create_report_buttons()

    def create_scrollable_frame(self):
        """إنشاء إطار قابل للتمرير"""
        # إنشاء Canvas للتمرير
        self.canvas = tk.Canvas(self.frame, bg=self.colors['surface'])
        self.scrollbar = tk.Scrollbar(self.frame, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ModernFrame(self.canvas, bg=self.colors['surface'])

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)

        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")

    def create_filter_section(self):
        """إنشاء منطقة الفلترة"""
        filter_frame = ModernLabelFrame(
            self.scrollable_frame,
            text="فلترة البيانات",
            bg=self.colors['surface'],
            fg=self.colors['primary']
        )
        filter_frame.pack(fill=tk.X, padx=20, pady=10)

        # متغيرات الفلترة
        self.filter_vars = {}

        # الصف الأول - التواريخ
        row1_frame = ModernFrame(filter_frame, bg=self.colors['surface'])
        row1_frame.pack(fill=tk.X, padx=10, pady=5)

        # تاريخ البداية
        tk.Label(
            row1_frame,
            text="من تاريخ:",
            bg=self.colors['surface'],
            fg=self.colors['text'],
            font=self.font
        ).grid(row=0, column=0, sticky="e", padx=5)

        self.filter_vars['start_date'] = tk.StringVar()
        start_date_entry = ModernEntry(
            row1_frame,
            textvariable=self.filter_vars['start_date'],
            width=12
        )
        start_date_entry.grid(row=0, column=1, sticky="w", padx=5)

        # تاريخ النهاية
        tk.Label(
            row1_frame,
            text="إلى تاريخ:",
            bg=self.colors['surface'],
            fg=self.colors['text'],
            font=self.font
        ).grid(row=0, column=2, sticky="e", padx=5)

        self.filter_vars['end_date'] = tk.StringVar()
        end_date_entry = ModernEntry(
            row1_frame,
            textvariable=self.filter_vars['end_date'],
            width=12
        )
        end_date_entry.grid(row=0, column=3, sticky="w", padx=5)

        # الصف الثاني - الفلاتر الأخرى
        row2_frame = ModernFrame(filter_frame, bg=self.colors['surface'])
        row2_frame.pack(fill=tk.X, padx=10, pady=5)

        # الاسم
        tk.Label(
            row2_frame,
            text="الاسم:",
            bg=self.colors['surface'],
            fg=self.colors['text'],
            font=self.font
        ).grid(row=0, column=0, sticky="e", padx=5)

        self.filter_vars['name'] = tk.StringVar()
        name_entry = ModernEntry(
            row2_frame,
            textvariable=self.filter_vars['name'],
            width=15
        )
        name_entry.grid(row=0, column=1, sticky="w", padx=5)

        # نوع العينة
        tk.Label(
            row2_frame,
            text="نوع العينة:",
            bg=self.colors['surface'],
            fg=self.colors['text'],
            font=self.font
        ).grid(row=0, column=2, sticky="e", padx=5)

        self.filter_vars['sample_type'] = tk.StringVar()
        sample_type_combo = ModernCombobox(
            row2_frame,
            textvariable=self.filter_vars['sample_type'],
            width=12
        )
        sample_type_combo.grid(row=0, column=3, sticky="w", padx=5)
        self.load_sample_types(sample_type_combo)

        # الصف الثالث - المزيد من الفلاتر
        row3_frame = ModernFrame(filter_frame, bg=self.colors['surface'])
        row3_frame.pack(fill=tk.X, padx=10, pady=5)

        # الجنس
        tk.Label(
            row3_frame,
            text="الجنس:",
            bg=self.colors['surface'],
            fg=self.colors['text'],
            font=self.font
        ).grid(row=0, column=0, sticky="e", padx=5)

        self.filter_vars['gender'] = tk.StringVar()
        gender_combo = ModernCombobox(
            row3_frame,
            textvariable=self.filter_vars['gender'],
            values=['', 'M', 'F'],
            width=12
        )
        gender_combo.grid(row=0, column=1, sticky="w", padx=5)

        # جهة الإرسال
        tk.Label(
            row3_frame,
            text="جهة الإرسال:",
            bg=self.colors['surface'],
            fg=self.colors['text'],
            font=self.font
        ).grid(row=0, column=2, sticky="e", padx=5)

        self.filter_vars['sender_organization'] = tk.StringVar()
        sender_combo = ModernCombobox(
            row3_frame,
            textvariable=self.filter_vars['sender_organization'],
            width=15
        )
        sender_combo.grid(row=0, column=3, sticky="w", padx=5)
        self.load_sender_organizations(sender_combo)

        # أزرار الفلترة
        buttons_frame = ModernFrame(filter_frame, bg=self.colors['surface'])
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        # زر تطبيق الفلتر
        filter_btn = ModernButton(
            buttons_frame,
            text="تطبيق الفلتر",
            command=self.apply_filter,
            bg=self.colors['primary'],
            fg='white',
            font=self.font
        )
        filter_btn.pack(side=tk.LEFT, padx=5)

        # زر مسح الفلتر
        clear_filter_btn = ModernButton(
            buttons_frame,
            text="مسح الفلتر",
            command=self.clear_filter,
            bg='#6C757D',
            fg='white',
            font=self.font
        )
        clear_filter_btn.pack(side=tk.LEFT, padx=5)

        # زر تحديث
        refresh_btn = ModernButton(
            buttons_frame,
            text="تحديث",
            command=self.refresh_data,
            bg='#28A745',
            fg='white',
            font=self.font
        )
        refresh_btn.pack(side=tk.LEFT, padx=5)

    def create_results_table(self):
        """إنشاء جدول النتائج"""
        table_frame = ModernLabelFrame(
            self.scrollable_frame,
            text="نتائج البحث",
            bg=self.colors['surface'],
            fg=self.colors['primary']
        )
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # إنشاء الجدول
        columns = ("الرقم الوطني", "الاسم", "العمر", "الجنس", "نوع العينة", "جهة الإرسال", "التحليل", "النتيجة", "تاريخ النتيجة")
        self.results_tree = ModernTreeview(table_frame, columns=columns, show='headings', height=10)

        # تعيين عناوين الأعمدة
        for col in columns:
            self.results_tree.heading(col, text=col)
            self.results_tree.column(col, width=100, anchor='center')

        # شريط التمرير
        results_scrollbar = tk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=results_scrollbar.set)

        self.results_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        results_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # ربط حدث النقر المزدوج
        self.results_tree.bind('<Double-1>', self.on_double_click)

    def create_statistics_section(self):
        """إنشاء منطقة الإحصائيات"""
        stats_frame = ModernLabelFrame(
            self.scrollable_frame,
            text="الإحصائيات",
            bg=self.colors['surface'],
            fg=self.colors['primary']
        )
        stats_frame.pack(fill=tk.X, padx=20, pady=10)

        # إطار الإحصائيات
        stats_content = ModernFrame(stats_frame, bg=self.colors['surface'])
        stats_content.pack(fill=tk.X, padx=10, pady=10)

        # الإحصائيات الأساسية
        self.stats_labels = {}

        stats_info = [
            ("total_samples", "إجمالي العينات"),
            ("positive_results", "النتائج الإيجابية"),
            ("negative_results", "النتائج السلبية"),
            ("pending_results", "النتائج المعلقة")
        ]

        row = 0
        col = 0
        for stat_key, stat_label in stats_info:
            # تسمية الإحصائية
            label = ModernLabel(
                stats_content,
                text=f"{stat_label}:",
                bg=self.colors['surface'],
                fg=self.colors['text'],
                font=self.font
            )
            label.grid(row=row, column=col, sticky="e", padx=10, pady=5)

            # قيمة الإحصائية
            value_label = ModernLabel(
                stats_content,
                text="0",
                bg=self.colors['surface'],
                fg=self.colors['primary'],
                font=('Arial', 10, 'bold')
            )
            value_label.grid(row=row, column=col+1, sticky="w", padx=10, pady=5)

            self.stats_labels[stat_key] = value_label

            col += 2
            if col >= 8:  # 4 إحصائيات في كل صف
                col = 0
                row += 1

    def create_report_buttons(self):
        """إنشاء أزرار التقارير"""
        buttons_frame = ModernLabelFrame(
            self.scrollable_frame,
            text="التقارير",
            bg=self.colors['surface'],
            fg=self.colors['primary']
        )
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)

        # إطار الأزرار
        buttons_content = ModernFrame(buttons_frame, bg=self.colors['surface'])
        buttons_content.pack(fill=tk.X, padx=10, pady=10)

        # زر طباعة تقرير مفرد
        single_report_btn = ModernButton(
            buttons_content,
            text="تقرير مفرد",
            command=self.generate_single_report,
            bg='#17A2B8',
            fg='white',
            font=self.font
        )
        single_report_btn.pack(side=tk.LEFT, padx=5)

        # زر طباعة تقرير جماعي
        group_report_btn = ModernButton(
            buttons_content,
            text="تقرير جماعي",
            command=self.generate_group_report,
            bg='#28A745',
            fg='white',
            font=self.font
        )
        group_report_btn.pack(side=tk.LEFT, padx=5)

        # زر تصدير Excel
        export_btn = ModernButton(
            buttons_content,
            text="تصدير Excel",
            command=self.export_to_excel,
            bg='#FFC107',
            fg='black',
            font=self.font
        )
        export_btn.pack(side=tk.LEFT, padx=5)

        # زر معاينة التقرير
        preview_btn = ModernButton(
            buttons_content,
            text="معاينة التقرير",
            command=self.preview_report,
            bg=self.colors['primary'],
            fg='white',
            font=self.font
        )
        preview_btn.pack(side=tk.LEFT, padx=5)

    def load_sample_types(self, widget):
        """تحميل أنواع العينات"""
        sample_types = self.db_manager.fetch_all("SELECT name FROM sample_types ORDER BY name")
        values = [''] + [row['name'] for row in sample_types]
        widget['values'] = values

    def load_sender_organizations(self, widget):
        """تحميل جهات الإرسال"""
        organizations = self.db_manager.fetch_all("SELECT name FROM sender_organizations ORDER BY name")
        values = [''] + [row['name'] for row in organizations]
        widget['values'] = values

    def apply_filter(self):
        """تطبيق الفلتر"""
        # بناء الاستعلام
        query = '''
            SELECT p.national_id, p.name, p.age, p.gender, p.sample_type,
                   p.sender_organization, t.name as test_name, pt.result, pt.result_date
            FROM patients p
            LEFT JOIN patient_tests pt ON p.id = pt.patient_id
            LEFT JOIN tests t ON pt.test_id = t.id
            WHERE 1=1
        '''

        params = []

        # فلتر التاريخ
        start_date = self.filter_vars['start_date'].get()
        if start_date:
            query += " AND p.sample_collection_date >= ?"
            params.append(start_date)

        end_date = self.filter_vars['end_date'].get()
        if end_date:
            query += " AND p.sample_collection_date <= ?"
            params.append(end_date)

        # فلتر الاسم
        name = self.filter_vars['name'].get()
        if name:
            query += " AND p.name LIKE ?"
            params.append(f"%{name}%")

        # فلتر نوع العينة
        sample_type = self.filter_vars['sample_type'].get()
        if sample_type:
            query += " AND p.sample_type = ?"
            params.append(sample_type)

        # فلتر الجنس
        gender = self.filter_vars['gender'].get()
        if gender:
            query += " AND p.gender = ?"
            params.append(gender)

        # فلتر جهة الإرسال
        sender_org = self.filter_vars['sender_organization'].get()
        if sender_org:
            query += " AND p.sender_organization = ?"
            params.append(sender_org)

        query += " ORDER BY p.national_id DESC"

        # تنفيذ الاستعلام
        results = self.db_manager.fetch_all(query, params)

        # مسح الجدول
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        # إدراج النتائج
        for result in results:
            gender_text = 'ذكر' if result['gender'] == 'M' else 'أنثى'

            self.results_tree.insert('', tk.END, values=(
                result['national_id'],
                result['name'],
                result['age'],
                gender_text,
                result['sample_type'],
                result['sender_organization'],
                result['test_name'] or '',
                result['result'] or '',
                result['result_date'] or ''
            ))

        # تحديث الإحصائيات
        self.update_statistics(results)

        messagebox.showinfo("نجح", f"تم العثور على {len(results)} نتيجة")

    def clear_filter(self):
        """مسح الفلتر"""
        for var in self.filter_vars.values():
            var.set("")
        self.refresh_data()

    def refresh_data(self):
        """تحديث البيانات"""
        # جلب جميع البيانات
        query = '''
            SELECT p.national_id, p.name, p.age, p.gender, p.sample_type,
                   p.sender_organization, t.name as test_name, pt.result, pt.result_date
            FROM patients p
            LEFT JOIN patient_tests pt ON p.id = pt.patient_id
            LEFT JOIN tests t ON pt.test_id = t.id
            ORDER BY p.national_id DESC
        '''

        results = self.db_manager.fetch_all(query)

        # مسح الجدول
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        # إدراج النتائج
        for result in results:
            gender_text = 'ذكر' if result['gender'] == 'M' else 'أنثى'

            self.results_tree.insert('', tk.END, values=(
                result['national_id'],
                result['name'],
                result['age'],
                gender_text,
                result['sample_type'],
                result['sender_organization'],
                result['test_name'] or '',
                result['result'] or '',
                result['result_date'] or ''
            ))

        # تحديث الإحصائيات
        self.update_statistics(results)

    def update_statistics(self, results):
        """تحديث الإحصائيات"""
        total_samples = len(results)
        positive_results = len([r for r in results if r['result'] == 'Positive'])
        negative_results = len([r for r in results if r['result'] == 'Negative'])
        pending_results = len([r for r in results if not r['result']])

        self.stats_labels['total_samples'].configure(text=str(total_samples))
        self.stats_labels['positive_results'].configure(text=str(positive_results))
        self.stats_labels['negative_results'].configure(text=str(negative_results))
        self.stats_labels['pending_results'].configure(text=str(pending_results))

    def on_double_click(self, event):
        """عند النقر المزدوج على النتيجة"""
        selection = self.results_tree.selection()
        if selection:
            item = self.results_tree.item(selection[0])
            values = item['values']

            # عرض تفاصيل المريض
            details = f"""
تفاصيل المريض:
الرقم الوطني: {values[0]}
الاسم: {values[1]}
العمر: {values[2]}
الجنس: {values[3]}
نوع العينة: {values[4]}
جهة الإرسال: {values[5]}
التحليل: {values[6]}
النتيجة: {values[7]}
تاريخ النتيجة: {values[8]}
            """

            messagebox.showinfo("تفاصيل المريض", details)

    def generate_single_report(self):
        """إنشاء تقرير مفرد"""
        selection = self.results_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مريض لإنشاء التقرير")
            return

        item = self.results_tree.item(selection[0])
        values = item['values']

        # إنشاء محتوى التقرير
        report_content = self.create_single_report_content(values)

        # عرض معاينة التقرير
        self.show_report_preview(report_content, f"تقرير المريض - {values[1]}")

    def generate_group_report(self):
        """إنشاء تقرير جماعي"""
        # جلب البيانات المفلترة
        items = self.results_tree.get_children()
        if not items:
            messagebox.showwarning("تحذير", "لا توجد بيانات لإنشاء التقرير")
            return

        # إنشاء محتوى التقرير
        report_content = self.create_group_report_content()

        # عرض معاينة التقرير
        self.show_report_preview(report_content, "التقرير الجماعي")

    def create_single_report_content(self, patient_data):
        """إنشاء محتوى التقرير المفرد"""
        # جلب إعدادات التقرير
        settings = self.get_report_settings()

        current_date = datetime.datetime.now().strftime("%Y-%m-%d")

        content = f"""
{settings['header_right']}
{settings['header_left']}

تقرير نتيجة فحص مختبري

الرقم الوطني: {patient_data[0]}
اسم المريض: {patient_data[1]}
العمر: {patient_data[2]} سنة
الجنس: {patient_data[3]}
نوع العينة: {patient_data[4]}
جهة الإرسال: {patient_data[5]}
نوع التحليل: {patient_data[6]}
النتيجة: {patient_data[7]}
تاريخ النتيجة: {patient_data[8]}

تاريخ طباعة التقرير: {current_date}

{settings['footer_address']}
{settings['footer_email']}
        """

        return content.strip()

    def create_group_report_content(self):
        """إنشاء محتوى التقرير الجماعي"""
        settings = self.get_report_settings()
        current_date = datetime.datetime.now().strftime("%Y-%m-%d")

        content = f"""
{settings['header_right']}
{settings['header_left']}

التقرير الإحصائي الشامل

تاريخ التقرير: {current_date}

الإحصائيات العامة:
إجمالي العينات: {self.stats_labels['total_samples'].cget('text')}
النتائج الإيجابية: {self.stats_labels['positive_results'].cget('text')}
النتائج السلبية: {self.stats_labels['negative_results'].cget('text')}
النتائج المعلقة: {self.stats_labels['pending_results'].cget('text')}

تفاصيل العينات:
"""

        # إضافة تفاصيل العينات
        items = self.results_tree.get_children()
        for i, item in enumerate(items, 1):
            values = self.results_tree.item(item)['values']
            content += f"""
{i}. {values[1]} - الرقم الوطني: {values[0]}
   العمر: {values[2]} - الجنس: {values[3]}
   نوع العينة: {values[4]} - النتيجة: {values[7]}
"""

        content += f"""

{settings['footer_address']}
{settings['footer_email']}
        """

        return content.strip()

    def get_report_settings(self):
        """الحصول على إعدادات التقرير"""
        settings = {}

        # جلب الإعدادات من قاعدة البيانات
        report_settings = self.db_manager.fetch_all("SELECT setting_name, setting_value FROM report_settings")

        for setting in report_settings:
            settings[setting['setting_name']] = setting['setting_value'] or ''

        return settings

    def show_report_preview(self, content, title):
        """عرض معاينة التقرير"""
        preview_window = tk.Toplevel(self.frame)
        preview_window.title(f"معاينة - {title}")
        preview_window.geometry("800x600")
        preview_window.configure(bg=self.colors['surface'])

        # منطقة النص
        text_frame = ModernFrame(preview_window)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        text_widget = ModernText(text_frame, wrap=tk.WORD, font=('Arial', 11))
        text_widget.pack(fill=tk.BOTH, expand=True)

        # إدراج المحتوى
        text_widget.insert(tk.END, content)
        text_widget.configure(state='disabled')

        # أزرار العمليات
        buttons_frame = ModernFrame(preview_window)
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)

        # زر الطباعة
        print_btn = ModernButton(
            buttons_frame,
            text="طباعة",
            command=lambda: self.print_report(content, title),
            bg='#28A745',
            fg='white',
            font=self.font
        )
        print_btn.pack(side=tk.LEFT, padx=5)

        # زر حفظ كملف
        save_btn = ModernButton(
            buttons_frame,
            text="حفظ كملف",
            command=lambda: self.save_report_to_file(content, title),
            bg='#17A2B8',
            fg='white',
            font=self.font
        )
        save_btn.pack(side=tk.LEFT, padx=5)

        # زر إغلاق
        close_btn = ModernButton(
            buttons_frame,
            text="إغلاق",
            command=preview_window.destroy,
            bg='#6C757D',
            fg='white',
            font=self.font
        )
        close_btn.pack(side=tk.RIGHT, padx=5)

    def print_report(self, content, title):
        """طباعة التقرير"""
        try:
            from printer_manager import PrinterManager
            printer = PrinterManager()

            if printer.print_report(content, title):
                messagebox.showinfo("نجح", "تم إرسال التقرير للطباعة")
            else:
                messagebox.showerror("خطأ", "فشل في طباعة التقرير")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء الطباعة: {str(e)}")

    def save_report_to_file(self, content, title):
        """حفظ التقرير كملف"""
        file_path = filedialog.asksaveasfilename(
            title="حفظ التقرير",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                messagebox.showinfo("نجح", f"تم حفظ التقرير في: {file_path}")
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ الملف: {str(e)}")

    def export_to_excel(self):
        """تصدير البيانات إلى Excel"""
        try:
            import pandas as pd

            # جمع البيانات من الجدول
            data = []
            items = self.results_tree.get_children()

            for item in items:
                values = self.results_tree.item(item)['values']
                data.append({
                    'الرقم الوطني': values[0],
                    'الاسم': values[1],
                    'العمر': values[2],
                    'الجنس': values[3],
                    'نوع العينة': values[4],
                    'جهة الإرسال': values[5],
                    'التحليل': values[6],
                    'النتيجة': values[7],
                    'تاريخ النتيجة': values[8]
                })

            if not data:
                messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
                return

            # إنشاء DataFrame
            df = pd.DataFrame(data)

            # اختيار مكان الحفظ
            file_path = filedialog.asksaveasfilename(
                title="تصدير إلى Excel",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
            )

            if file_path:
                df.to_excel(file_path, index=False, engine='openpyxl')
                messagebox.showinfo("نجح", f"تم تصدير البيانات إلى: {file_path}")

        except ImportError:
            messagebox.showerror("خطأ", "يرجى تثبيت مكتبة pandas و openpyxl لتصدير Excel")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء التصدير: {str(e)}")

    def preview_report(self):
        """معاينة التقرير"""
        selection = self.results_tree.selection()
        if selection:
            self.generate_single_report()
        else:
            self.generate_group_report()

    def show(self):
        """عرض التبويب"""
        if self.frame:
            self.frame.pack(fill=tk.BOTH, expand=True)
            self.refresh_data()

    def hide(self):
        """إخفاء التبويب"""
        if self.frame:
            self.frame.pack_forget()