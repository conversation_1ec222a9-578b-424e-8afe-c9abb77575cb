#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تبويب النتائج
Results Tab
"""

import tkinter as tk
from tkinter import ttk, messagebox
import datetime
from ui_components import *

class ResultsTab:
    def __init__(self, parent, db_manager, colors, font):
        self.parent = parent
        self.db_manager = db_manager
        self.colors = colors
        self.font = font
        self.frame = None
        self.current_batch_id = None
        self.create_ui()
        
    def create_ui(self):
        """إنشاء واجهة النتائج"""
        self.frame = ModernFrame(self.parent, bg=self.colors['surface'])
        
        # عنوان التبويب
        title_label = ModernLabel(
            self.frame,
            text="إدخال النتائج",
            font=('Arial', 16, 'bold'),
            fg=self.colors['primary'],
            bg=self.colors['surface']
        )
        title_label.pack(pady=20)
        
        # إنشاء الإطار الرئيسي
        main_frame = ModernFrame(self.frame, bg=self.colors['surface'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20)
        
        # إنشاء منطقة اختيار الوجبة
        self.create_batch_selection(main_frame)
        
        # إنشاء جدول العينات
        self.create_samples_table(main_frame)
        
        # إنشاء منطقة إدخال النتائج
        self.create_results_input(main_frame)
        
    def create_batch_selection(self, parent):
        """إنشاء منطقة اختيار الوجبة"""
        selection_frame = ModernLabelFrame(
            parent,
            text="اختيار الوجبة",
            bg=self.colors['surface'],
            fg=self.colors['primary']
        )
        selection_frame.pack(fill=tk.X, pady=10)
        
        # إطار الحقول
        fields_frame = ModernFrame(selection_frame, bg=self.colors['surface'])
        fields_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # رقم الوجبة
        tk.Label(
            fields_frame,
            text="رقم الوجبة:",
            bg=self.colors['surface'],
            fg=self.colors['text'],
            font=self.font
        ).pack(side=tk.LEFT, padx=10)
        
        self.batch_number_var = tk.StringVar()
        batch_entry = ModernEntry(
            fields_frame,
            textvariable=self.batch_number_var,
            width=15
        )
        batch_entry.pack(side=tk.LEFT, padx=10)
        
        # زر عرض
        show_btn = ModernButton(
            fields_frame,
            text="عرض",
            command=self.load_batch_samples,
            bg=self.colors['primary'],
            fg='white',
            font=self.font
        )
        show_btn.pack(side=tk.LEFT, padx=10)
        
        # معلومات الوجبة
        self.batch_info_label = ModernLabel(
            fields_frame,
            text="",
            bg=self.colors['surface'],
            fg=self.colors['text_secondary'],
            font=self.font
        )
        self.batch_info_label.pack(side=tk.LEFT, padx=20)
        
    def create_samples_table(self, parent):
        """إنشاء جدول العينات"""
        table_frame = ModernLabelFrame(
            parent,
            text="عينات الوجبة",
            bg=self.colors['surface'],
            fg=self.colors['primary']
        )
        table_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # إنشاء الجدول
        columns = ("الرقم الوطني", "الاسم", "نوع العينة", "التحليل", "الفني", "النتيجة", "تاريخ النتيجة")
        self.samples_tree = ModernTreeview(table_frame, columns=columns, show='headings', height=12)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.samples_tree.heading(col, text=col)
            self.samples_tree.column(col, width=120, anchor='center')
            
        # شريط التمرير
        samples_scrollbar = tk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.samples_tree.yview)
        self.samples_tree.configure(yscrollcommand=samples_scrollbar.set)
        
        self.samples_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        samples_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط حدث النقر
        self.samples_tree.bind('<ButtonRelease-1>', self.on_sample_select)
        
    def create_results_input(self, parent):
        """إنشاء منطقة إدخال النتائج"""
        input_frame = ModernLabelFrame(
            parent,
            text="إدخال النتيجة",
            bg=self.colors['surface'],
            fg=self.colors['primary']
        )
        input_frame.pack(fill=tk.X, pady=10)
        
        # إطار الحقول
        fields_frame = ModernFrame(input_frame, bg=self.colors['surface'])
        fields_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # معلومات العينة المحددة
        self.selected_sample_label = ModernLabel(
            fields_frame,
            text="لم يتم اختيار عينة",
            bg=self.colors['surface'],
            fg=self.colors['text_secondary'],
            font=('Arial', 10, 'bold')
        )
        self.selected_sample_label.pack(pady=10)
        
        # النتيجة
        results_frame = ModernFrame(fields_frame, bg=self.colors['surface'])
        results_frame.pack(fill=tk.X, pady=10)
        
        tk.Label(
            results_frame,
            text="النتيجة:",
            bg=self.colors['surface'],
            fg=self.colors['text'],
            font=self.font
        ).pack(side=tk.LEFT, padx=10)
        
        self.result_var = tk.StringVar()
        result_combo = ModernCombobox(
            results_frame,
            textvariable=self.result_var,
            values=['Negative', 'Positive', 'Retest', 'Recollection', 'Sent', 'TND'],
            width=15
        )
        result_combo.pack(side=tk.LEFT, padx=10)
        
        # تاريخ النتيجة
        tk.Label(
            results_frame,
            text="تاريخ النتيجة:",
            bg=self.colors['surface'],
            fg=self.colors['text'],
            font=self.font
        ).pack(side=tk.LEFT, padx=10)
        
        self.result_date_var = tk.StringVar(
            value=datetime.date.today().strftime("%Y-%m-%d")
        )
        result_date_entry = ModernEntry(
            results_frame,
            textvariable=self.result_date_var,
            width=15
        )
        result_date_entry.pack(side=tk.LEFT, padx=10)
        
        # أزرار العمليات
        buttons_frame = ModernFrame(fields_frame, bg=self.colors['surface'])
        buttons_frame.pack(fill=tk.X, pady=10)
        
        # زر حفظ النتيجة
        save_btn = ModernButton(
            buttons_frame,
            text="حفظ النتيجة",
            command=self.save_result,
            bg='#28A745',
            fg='white',
            font=self.font
        )
        save_btn.pack(side=tk.LEFT, padx=5)
        
        # زر حفظ جميع النتائج
        save_all_btn = ModernButton(
            buttons_frame,
            text="حفظ جميع النتائج",
            command=self.save_all_results,
            bg='#17A2B8',
            fg='white',
            font=self.font
        )
        save_all_btn.pack(side=tk.LEFT, padx=5)
        
        # زر مسح النتيجة
        clear_btn = ModernButton(
            buttons_frame,
            text="مسح النتيجة",
            command=self.clear_result,
            bg='#6C757D',
            fg='white',
            font=self.font
        )
        clear_btn.pack(side=tk.LEFT, padx=5)
        
        # إطار النتائج السريعة
        quick_frame = ModernFrame(fields_frame, bg=self.colors['surface'])
        quick_frame.pack(fill=tk.X, pady=10)
        
        tk.Label(
            quick_frame,
            text="النتائج السريعة:",
            bg=self.colors['surface'],
            fg=self.colors['text'],
            font=self.font
        ).pack(side=tk.LEFT, padx=10)
        
        # أزرار النتائج السريعة
        quick_results = ['Negative', 'Positive', 'Retest', 'Recollection', 'Sent', 'TND']
        colors_map = {
            'Negative': '#28A745',
            'Positive': '#DC3545',
            'Retest': '#FFC107',
            'Recollection': '#17A2B8',
            'Sent': '#6F42C1',
            'TND': '#6C757D'
        }
        
        for result in quick_results:
            btn = ModernButton(
                quick_frame,
                text=result,
                command=lambda r=result: self.quick_result(r),
                bg=colors_map.get(result, self.colors['primary']),
                fg='white' if result != 'Retest' else 'black',
                font=('Arial', 8, 'bold'),
                width=8
            )
            btn.pack(side=tk.LEFT, padx=2)
            
        # متغيرات للعينة المحددة
        self.selected_patient_test_id = None
        
    def load_batch_samples(self):
        """تحميل عينات الوجبة"""
        batch_number = self.batch_number_var.get().strip()
        if not batch_number:
            messagebox.showwarning("تحذير", "يرجى إدخال رقم الوجبة")
            return
            
        try:
            batch_number = int(batch_number)
        except ValueError:
            messagebox.showerror("خطأ", "رقم الوجبة يجب أن يكون رقماً")
            return
            
        # البحث عن الوجبة
        batch = self.db_manager.fetch_one(
            "SELECT * FROM batches WHERE batch_number = ?",
            (batch_number,)
        )
        
        if not batch:
            messagebox.showerror("خطأ", "لم يتم العثور على الوجبة")
            return
            
        self.current_batch_id = batch['id']
        
        # عرض معلومات الوجبة
        batch_info = f"الفترة: {batch['start_date']} إلى {batch['end_date']} | الحالة: {batch['status']}"
        self.batch_info_label.configure(text=batch_info)
        
        # مسح الجدول
        for item in self.samples_tree.get_children():
            self.samples_tree.delete(item)
            
        # جلب العينات
        query = '''
            SELECT pt.id, p.national_id, p.name, p.sample_type,
                   t.name as test_name, pt.technician_name,
                   pt.result, pt.result_date
            FROM patient_tests pt
            JOIN patients p ON pt.patient_id = p.id
            JOIN tests t ON pt.test_id = t.id
            WHERE pt.batch_id = ?
            ORDER BY p.national_id
        '''
        
        samples = self.db_manager.fetch_all(query, (self.current_batch_id,))
        
        # إدراج البيانات
        for sample in samples:
            result_text = sample['result'] or ''
            result_date = sample['result_date'] or ''
            
            self.samples_tree.insert('', tk.END, values=(
                sample['national_id'],
                sample['name'],
                sample['sample_type'],
                sample['test_name'],
                sample['technician_name'] or '',
                result_text,
                result_date
            ), tags=(sample['id'],))
            
        messagebox.showinfo("نجح", f"تم تحميل {len(samples)} عينة")
        
    def on_sample_select(self, event):
        """عند اختيار عينة"""
        selection = self.samples_tree.selection()
        if selection:
            item = self.samples_tree.item(selection[0])
            patient_test_id = item['tags'][0]
            values = item['values']
            
            self.selected_patient_test_id = patient_test_id
            
            # عرض معلومات العينة المحددة
            sample_info = f"المريض: {values[1]} | الرقم الوطني: {values[0]} | التحليل: {values[3]}"
            self.selected_sample_label.configure(text=sample_info)
            
            # تحميل النتيجة الحالية
            current_result = values[5]
            if current_result:
                self.result_var.set(current_result)
                
            current_date = values[6]
            if current_date:
                self.result_date_var.set(current_date)
                
    def save_result(self):
        """حفظ النتيجة للعينة المحددة"""
        if not self.selected_patient_test_id:
            messagebox.showwarning("تحذير", "يرجى اختيار عينة أولاً")
            return
            
        result = self.result_var.get()
        if not result:
            messagebox.showwarning("تحذير", "يرجى اختيار النتيجة")
            return
            
        result_date = self.result_date_var.get()
        
        try:
            # حفظ النتيجة
            query = '''
                UPDATE patient_tests 
                SET result = ?, result_date = ?
                WHERE id = ?
            '''
            
            self.db_manager.execute_query(query, (result, result_date, self.selected_patient_test_id))
            
            messagebox.showinfo("نجح", "تم حفظ النتيجة بنجاح")
            
            # تحديث الجدول
            self.load_batch_samples()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ النتيجة: {str(e)}")
            
    def save_all_results(self):
        """حفظ جميع النتائج بنفس القيمة"""
        if not self.current_batch_id:
            messagebox.showwarning("تحذير", "يرجى اختيار وجبة أولاً")
            return
            
        result = self.result_var.get()
        if not result:
            messagebox.showwarning("تحذير", "يرجى اختيار النتيجة")
            return
            
        if not messagebox.askyesno("تأكيد", f"هل تريد تطبيق النتيجة '{result}' على جميع العينات في الوجبة؟"):
            return
            
        result_date = self.result_date_var.get()
        
        try:
            # تحديث جميع النتائج في الوجبة
            query = '''
                UPDATE patient_tests 
                SET result = ?, result_date = ?
                WHERE batch_id = ? AND result IS NULL
            '''
            
            cursor = self.db_manager.execute_query(query, (result, result_date, self.current_batch_id))
            
            if cursor:
                updated_count = cursor.rowcount
                messagebox.showinfo("نجح", f"تم تحديث {updated_count} نتيجة")
                
                # تحديث الجدول
                self.load_batch_samples()
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ النتائج: {str(e)}")
            
    def clear_result(self):
        """مسح النتيجة"""
        if not self.selected_patient_test_id:
            messagebox.showwarning("تحذير", "يرجى اختيار عينة أولاً")
            return
            
        if messagebox.askyesno("تأكيد", "هل تريد مسح النتيجة؟"):
            try:
                query = '''
                    UPDATE patient_tests 
                    SET result = NULL, result_date = NULL
                    WHERE id = ?
                '''
                
                self.db_manager.execute_query(query, (self.selected_patient_test_id,))
                
                messagebox.showinfo("نجح", "تم مسح النتيجة")
                
                # مسح الحقول
                self.result_var.set("")
                self.result_date_var.set(datetime.date.today().strftime("%Y-%m-%d"))
                
                # تحديث الجدول
                self.load_batch_samples()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء مسح النتيجة: {str(e)}")
                
    def quick_result(self, result):
        """تطبيق نتيجة سريعة"""
        self.result_var.set(result)
        self.save_result()
        
    def show(self):
        """عرض التبويب"""
        if self.frame:
            self.frame.pack(fill=tk.BOTH, expand=True)
            
    def hide(self):
        """إخفاء التبويب"""
        if self.frame:
            self.frame.pack_forget()
