#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل تطبيق مختبر الصحة العامة المركزي - ذي قار
Central Public Health Laboratory Application Launcher
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import subprocess
import webbrowser

def show_welcome_screen():
    """عرض شاشة الترحيب"""
    root = tk.Tk()
    root.title("مختبر الصحة العامة المركزي - ذي قار")
    root.geometry("600x400")
    root.configure(bg='#F8F9FA')
    root.resizable(False, False)
    
    # توسيط النافذة
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (600 // 2)
    y = (root.winfo_screenheight() // 2) - (400 // 2)
    root.geometry(f"600x400+{x}+{y}")
    
    # الإطار الرئيسي
    main_frame = tk.Frame(root, bg='#FFFFFF', relief=tk.RAISED, bd=2)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    
    # العنوان
    title_label = tk.Label(
        main_frame,
        text="مختبر الصحة العامة المركزي",
        font=('Arial', 18, 'bold'),
        fg='#2E86AB',
        bg='#FFFFFF'
    )
    title_label.pack(pady=20)
    
    subtitle_label = tk.Label(
        main_frame,
        text="ذي قار - العراق",
        font=('Arial', 14),
        fg='#6C757D',
        bg='#FFFFFF'
    )
    subtitle_label.pack(pady=5)
    
    # خط فاصل
    separator = tk.Frame(main_frame, height=2, bg='#2E86AB')
    separator.pack(fill=tk.X, padx=50, pady=20)
    
    # معلومات التطبيق
    info_label = tk.Label(
        main_frame,
        text="نظام إدارة شامل للمختبر يتضمن:\n• إدخال بيانات المرضى والعينات\n• إدارة الوجبات والعمل\n• إدخال النتائج\n• التقارير والإحصائيات\n• الإعدادات والتخصيص",
        font=('Arial', 11),
        fg='#212529',
        bg='#FFFFFF',
        justify=tk.CENTER
    )
    info_label.pack(pady=20)
    
    # أزرار العمليات
    buttons_frame = tk.Frame(main_frame, bg='#FFFFFF')
    buttons_frame.pack(pady=20)
    
    def start_app():
        root.destroy()
        launch_main_app()
    
    def run_test():
        root.destroy()
        launch_test_app()
    
    def show_help():
        try:
            if os.path.exists("README.md"):
                os.startfile("README.md")
            else:
                messagebox.showinfo("المساعدة", "ملف المساعدة غير متوفر")
        except:
            messagebox.showinfo("المساعدة", "لا يمكن فتح ملف المساعدة")
    
    # زر تشغيل التطبيق
    start_btn = tk.Button(
        buttons_frame,
        text="تشغيل التطبيق",
        command=start_app,
        bg='#28A745',
        fg='white',
        font=('Arial', 12, 'bold'),
        width=15,
        height=2,
        relief=tk.RAISED,
        bd=3,
        cursor='hand2'
    )
    start_btn.pack(side=tk.LEFT, padx=10)
    
    # زر اختبار التطبيق
    test_btn = tk.Button(
        buttons_frame,
        text="اختبار التطبيق",
        command=run_test,
        bg='#17A2B8',
        fg='white',
        font=('Arial', 12, 'bold'),
        width=15,
        height=2,
        relief=tk.RAISED,
        bd=3,
        cursor='hand2'
    )
    test_btn.pack(side=tk.LEFT, padx=10)
    
    # زر المساعدة
    help_btn = tk.Button(
        buttons_frame,
        text="المساعدة",
        command=show_help,
        bg='#FFC107',
        fg='black',
        font=('Arial', 12, 'bold'),
        width=15,
        height=2,
        relief=tk.RAISED,
        bd=3,
        cursor='hand2'
    )
    help_btn.pack(side=tk.LEFT, padx=10)
    
    # معلومات الإصدار
    version_label = tk.Label(
        main_frame,
        text="الإصدار 1.0.0 - 2024",
        font=('Arial', 9),
        fg='#6C757D',
        bg='#FFFFFF'
    )
    version_label.pack(side=tk.BOTTOM, pady=10)
    
    root.mainloop()

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 8):
        messagebox.showerror(
            "خطأ في الإصدار",
            f"يتطلب التطبيق Python 3.8 أو أحدث\nالإصدار الحالي: {sys.version}"
        )
        return False
    return True

def check_required_files():
    """التحقق من وجود الملفات المطلوبة"""
    required_files = [
        'main.py',
        'database.py',
        'ui_components.py',
        'data_entry.py',
        'work_tab.py',
        'results_tab.py',
        'reports_tab.py',
        'settings_tab.py',
        'printer_manager.py',
        'config.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        messagebox.showerror(
            "ملفات مفقودة",
            f"الملفات التالية مفقودة:\n" + "\n".join(missing_files)
        )
        return False
    return True

def launch_main_app():
    """تشغيل التطبيق الرئيسي"""
    try:
        if os.path.exists('start_app.py'):
            subprocess.run([sys.executable, 'start_app.py'])
        else:
            subprocess.run([sys.executable, 'main.py'])
    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في تشغيل التطبيق:\n{str(e)}")

def launch_test_app():
    """تشغيل اختبار التطبيق"""
    try:
        if os.path.exists('test_app.py'):
            subprocess.run([sys.executable, 'test_app.py'])
        else:
            messagebox.showinfo("اختبار", "ملف الاختبار غير متوفر")
    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في تشغيل الاختبار:\n{str(e)}")

def main():
    """الدالة الرئيسية"""
    print("========================================")
    print("  مختبر الصحة العامة المركزي - ذي قار")
    print("  Central Public Health Laboratory")
    print("========================================")
    print()
    
    # التحقق من المتطلبات
    if not check_python_version():
        return
    
    if not check_required_files():
        return
    
    # عرض شاشة الترحيب
    show_welcome_screen()

if __name__ == "__main__":
    main()
