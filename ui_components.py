#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مكونات واجهة المستخدم الحديثة
Modern UI Components
"""

import tkinter as tk
from tkinter import ttk

class ModernButton(tk.Button):
    """زر حديث ثلاثي الأبعاد"""
    def __init__(self, parent, **kwargs):
        # الإعدادات الافتراضية
        default_config = {
            'relief': tk.RAISED,
            'bd': 3,
            'font': ('Arial', 10, 'bold'),
            'cursor': 'hand2',
            'activebackground': '#4A90E2',
            'activeforeground': 'white'
        }
        
        # دمج الإعدادات المخصصة مع الافتراضية
        for key, value in default_config.items():
            if key not in kwargs:
                kwargs[key] = value
                
        super().__init__(parent, **kwargs)
        
        # إضافة تأثيرات التفاعل
        self.bind("<Enter>", self.on_enter)
        self.bind("<Leave>", self.on_leave)
        self.bind("<Button-1>", self.on_click)
        self.bind("<ButtonRelease-1>", self.on_release)
        
        # حفظ الألوان الأصلية
        self.original_bg = kwargs.get('bg', '#2E86AB')
        self.original_fg = kwargs.get('fg', 'white')
        
    def on_enter(self, event):
        """عند دخول المؤشر"""
        self.configure(bg='#4A90E2', relief=tk.RAISED, bd=4)
        
    def on_leave(self, event):
        """عند خروج المؤشر"""
        self.configure(bg=self.original_bg, relief=tk.RAISED, bd=3)
        
    def on_click(self, event):
        """عند الضغط"""
        self.configure(relief=tk.SUNKEN, bd=2)
        
    def on_release(self, event):
        """عند تحرير الضغط"""
        self.configure(relief=tk.RAISED, bd=3)

class ModernFrame(tk.Frame):
    """إطار حديث مع حدود مميزة"""
    def __init__(self, parent, **kwargs):
        default_config = {
            'relief': tk.FLAT,
            'bd': 1,
            'bg': '#FFFFFF'
        }
        
        for key, value in default_config.items():
            if key not in kwargs:
                kwargs[key] = value
                
        super().__init__(parent, **kwargs)

class ModernEntry(tk.Entry):
    """حقل إدخال حديث"""
    def __init__(self, parent, **kwargs):
        default_config = {
            'relief': tk.SUNKEN,
            'bd': 2,
            'font': ('Arial', 10),
            'bg': '#FFFFFF',
            'fg': '#212529',
            'insertbackground': '#2E86AB'
        }
        
        for key, value in default_config.items():
            if key not in kwargs:
                kwargs[key] = value
                
        super().__init__(parent, **kwargs)
        
        # إضافة تأثيرات التركيز
        self.bind("<FocusIn>", self.on_focus_in)
        self.bind("<FocusOut>", self.on_focus_out)
        
    def on_focus_in(self, event):
        """عند التركيز"""
        self.configure(bg='#F0F8FF', bd=3)
        
    def on_focus_out(self, event):
        """عند فقدان التركيز"""
        self.configure(bg='#FFFFFF', bd=2)

class ModernCombobox(ttk.Combobox):
    """قائمة منسدلة حديثة"""
    def __init__(self, parent, **kwargs):
        default_config = {
            'font': ('Arial', 10),
            'state': 'readonly'
        }
        
        for key, value in default_config.items():
            if key not in kwargs:
                kwargs[key] = value
                
        super().__init__(parent, **kwargs)

class ModernText(tk.Text):
    """منطقة نص حديثة"""
    def __init__(self, parent, **kwargs):
        default_config = {
            'relief': tk.SUNKEN,
            'bd': 2,
            'font': ('Arial', 10),
            'bg': '#FFFFFF',
            'fg': '#212529',
            'insertbackground': '#2E86AB',
            'wrap': tk.WORD
        }
        
        for key, value in default_config.items():
            if key not in kwargs:
                kwargs[key] = value
                
        super().__init__(parent, **kwargs)
        
        # إضافة شريط التمرير
        if kwargs.get('scrollbar', True):
            self.scrollbar = tk.Scrollbar(parent)
            self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            self.configure(yscrollcommand=self.scrollbar.set)
            self.scrollbar.configure(command=self.yview)

class ModernTreeview(ttk.Treeview):
    """جدول حديث"""
    def __init__(self, parent, **kwargs):
        default_config = {
            'selectmode': 'extended'
        }
        
        for key, value in default_config.items():
            if key not in kwargs:
                kwargs[key] = value
                
        super().__init__(parent, **kwargs)
        
        # تطبيق الأنماط
        style = ttk.Style()
        style.theme_use('clam')
        
        # تخصيص ألوان الجدول
        style.configure("Treeview",
                       background="#FFFFFF",
                       foreground="#212529",
                       rowheight=25,
                       fieldbackground="#FFFFFF")
        
        style.configure("Treeview.Heading",
                       background="#2E86AB",
                       foreground="white",
                       font=('Arial', 10, 'bold'))
        
        style.map('Treeview',
                 background=[('selected', '#4A90E2')])

class ModernLabel(tk.Label):
    """تسمية حديثة"""
    def __init__(self, parent, **kwargs):
        default_config = {
            'font': ('Arial', 10),
            'bg': '#FFFFFF',
            'fg': '#212529'
        }
        
        for key, value in default_config.items():
            if key not in kwargs:
                kwargs[key] = value
                
        super().__init__(parent, **kwargs)

class ModernLabelFrame(tk.LabelFrame):
    """إطار مع عنوان حديث"""
    def __init__(self, parent, **kwargs):
        default_config = {
            'font': ('Arial', 10, 'bold'),
            'bg': '#FFFFFF',
            'fg': '#2E86AB',
            'relief': tk.GROOVE,
            'bd': 2
        }
        
        for key, value in default_config.items():
            if key not in kwargs:
                kwargs[key] = value
                
        super().__init__(parent, **kwargs)

class ModernCheckbutton(tk.Checkbutton):
    """مربع اختيار حديث"""
    def __init__(self, parent, **kwargs):
        default_config = {
            'font': ('Arial', 10),
            'bg': '#FFFFFF',
            'fg': '#212529',
            'activebackground': '#F0F8FF',
            'selectcolor': '#4A90E2'
        }
        
        for key, value in default_config.items():
            if key not in kwargs:
                kwargs[key] = value
                
        super().__init__(parent, **kwargs)

class ModernRadiobutton(tk.Radiobutton):
    """زر اختيار حديث"""
    def __init__(self, parent, **kwargs):
        default_config = {
            'font': ('Arial', 10),
            'bg': '#FFFFFF',
            'fg': '#212529',
            'activebackground': '#F0F8FF',
            'selectcolor': '#4A90E2'
        }
        
        for key, value in default_config.items():
            if key not in kwargs:
                kwargs[key] = value
                
        super().__init__(parent, **kwargs)

class ModernScale(tk.Scale):
    """شريط تمرير حديث"""
    def __init__(self, parent, **kwargs):
        default_config = {
            'font': ('Arial', 10),
            'bg': '#FFFFFF',
            'fg': '#212529',
            'activebackground': '#4A90E2',
            'troughcolor': '#E9ECEF'
        }
        
        for key, value in default_config.items():
            if key not in kwargs:
                kwargs[key] = value
                
        super().__init__(parent, **kwargs)

class SearchFrame(ModernFrame):
    """إطار البحث المتقدم"""
    def __init__(self, parent, search_callback=None, **kwargs):
        super().__init__(parent, **kwargs)
        self.search_callback = search_callback
        self.create_search_ui()
        
    def create_search_ui(self):
        """إنشاء واجهة البحث"""
        # عنوان البحث
        search_label = ModernLabel(self, text="البحث:", font=('Arial', 12, 'bold'))
        search_label.pack(pady=5)
        
        # إطار البحث
        search_frame = ModernFrame(self)
        search_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # حقل البحث
        self.search_entry = ModernEntry(search_frame, width=30)
        self.search_entry.pack(side=tk.LEFT, padx=(0, 10))
        self.search_entry.bind('<KeyRelease>', self.on_search_change)
        
        # زر البحث
        search_btn = ModernButton(
            search_frame,
            text="بحث",
            command=self.perform_search,
            bg='#28A745',
            fg='white'
        )
        search_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # زر مسح البحث
        clear_btn = ModernButton(
            search_frame,
            text="مسح",
            command=self.clear_search,
            bg='#DC3545',
            fg='white'
        )
        clear_btn.pack(side=tk.LEFT)
        
    def on_search_change(self, event):
        """عند تغيير نص البحث"""
        if self.search_callback:
            self.search_callback(self.search_entry.get())
            
    def perform_search(self):
        """تنفيذ البحث"""
        if self.search_callback:
            self.search_callback(self.search_entry.get())
            
    def clear_search(self):
        """مسح البحث"""
        self.search_entry.delete(0, tk.END)
        if self.search_callback:
            self.search_callback("")
            
    def get_search_text(self):
        """الحصول على نص البحث"""
        return self.search_entry.get()
