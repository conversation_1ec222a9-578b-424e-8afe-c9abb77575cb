#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد تطبيق مختبر الصحة العامة المركزي
Central Public Health Laboratory Application Setup
"""

from setuptools import setup, find_packages
import os

# قراءة ملف README
def read_readme():
    with open("README.md", "r", encoding="utf-8") as f:
        return f.read()

# قراءة المتطلبات
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as f:
        return [line.strip() for line in f if line.strip() and not line.startswith("#")]

setup(
    name="central-public-health-lab",
    version="1.0.0",
    description="نظام إدارة مختبر الصحة العامة المركزي - ذي قار",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    author="Lab Management Team",
    author_email="<EMAIL>",
    url="https://github.com/lab-management/central-health-lab",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Healthcare Industry",
        "License :: OSI Approved :: MIT License",
        "Operating System :: Microsoft :: Windows",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Medical Science Apps.",
        "Natural Language :: Arabic",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "black>=22.0.0",
            "flake8>=5.0.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "lab-management=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.md", "*.txt", "*.bat"],
    },
    data_files=[
        ("", ["README.md", "requirements.txt"]),
    ],
    zip_safe=False,
    keywords="laboratory management healthcare medical lab database",
    project_urls={
        "Bug Reports": "https://github.com/lab-management/central-health-lab/issues",
        "Source": "https://github.com/lab-management/central-health-lab",
        "Documentation": "https://github.com/lab-management/central-health-lab/wiki",
    },
)
