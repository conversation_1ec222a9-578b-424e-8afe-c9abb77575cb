# دليل التشغيل السريع
## Quick Start Guide

---

## 🚀 التشغيل السريع (3 خطوات)

### 1️⃣ تثبيت Python
- حمل Python من: https://www.python.org/downloads/
- تأكد من اختيار "Add Python to PATH"

### 2️⃣ تثبيت المكتبات
انقر مزدوج على: `install.bat`

### 3️⃣ تشغيل التطبيق
انقر مزدوج على: `START.bat`

---

## 📋 الملفات المهمة

| الملف | الوظيفة |
|-------|---------|
| `START.bat` | **تشغيل التطبيق** |
| `install.bat` | تثبيت المكتبات |
| `launch.py` | مشغل التطبيق مع الخيارات |
| `test_app.py` | اختبار التطبيق |

---

## 🔧 حل المشاكل الشائعة

### ❌ "Python was not found"
**الحل**: تثبيت Python من الموقع الرسمي

### ❌ "No module named..."
**الحل**: تشغيل `install.bat`

### ❌ مشاكل الطباعة
**الحل**: التأكد من تشغيل الطابعة وتعريفها

### ❌ مشاكل قاعدة البيانات
**الحل**: حذف ملف `lab_database.db` وإعادة التشغيل

---

## 📞 الدعم

للحصول على المساعدة:
1. راجع ملف `README.md`
2. راجع ملف `PROJECT_SUMMARY.md`
3. تشغيل `test_app.py` للتشخيص

---

## ✅ قائمة التحقق

- [ ] تم تثبيت Python 3.8+
- [ ] تم تشغيل install.bat
- [ ] الطابعة متصلة ومعرفة
- [ ] جميع ملفات التطبيق موجودة
- [ ] تم تشغيل START.bat بنجاح

---

**نصيحة**: احتفظ بنسخة احتياطية من مجلد التطبيق كاملاً
