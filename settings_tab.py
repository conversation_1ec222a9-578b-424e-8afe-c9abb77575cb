#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تبويب الإعدادات
Settings Tab
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from ui_components import *

class SettingsTab:
    def __init__(self, parent, db_manager, colors, font):
        self.parent = parent
        self.db_manager = db_manager
        self.colors = colors
        self.font = font
        self.frame = None
        self.create_ui()
        
    def create_ui(self):
        """إنشاء واجهة الإعدادات"""
        self.frame = ModernFrame(self.parent, bg=self.colors['surface'])
        
        # عنوان التبويب
        title_label = ModernLabel(
            self.frame,
            text="الإعدادات",
            font=('Arial', 16, 'bold'),
            fg=self.colors['primary'],
            bg=self.colors['surface']
        )
        title_label.pack(pady=20)
        
        # إنشاء دفتر التبويبات للإعدادات
        self.notebook = ttk.Notebook(self.frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # إنشاء التبويبات الفرعية
        self.create_sample_types_tab()
        self.create_organizations_tab()
        self.create_tests_tab()
        self.create_technicians_tab()
        self.create_report_settings_tab()
        
    def create_sample_types_tab(self):
        """إنشاء تبويب أنواع العينات"""
        tab_frame = ModernFrame(self.notebook, bg=self.colors['surface'])
        self.notebook.add(tab_frame, text="أنواع العينات")
        
        # إطار الإدخال
        input_frame = ModernLabelFrame(
            tab_frame,
            text="إضافة/تعديل نوع العينة",
            bg=self.colors['surface'],
            fg=self.colors['primary']
        )
        input_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # حقول الإدخال
        fields_frame = ModernFrame(input_frame, bg=self.colors['surface'])
        fields_frame.pack(fill=tk.X, padx=10, pady=10)
        
        tk.Label(
            fields_frame,
            text="اسم نوع العينة:",
            bg=self.colors['surface'],
            fg=self.colors['text'],
            font=self.font
        ).grid(row=0, column=0, sticky="e", padx=5, pady=5)
        
        self.sample_type_name_var = tk.StringVar()
        sample_type_entry = ModernEntry(
            fields_frame,
            textvariable=self.sample_type_name_var,
            width=30
        )
        sample_type_entry.grid(row=0, column=1, sticky="w", padx=5, pady=5)
        
        tk.Label(
            fields_frame,
            text="الوصف:",
            bg=self.colors['surface'],
            fg=self.colors['text'],
            font=self.font
        ).grid(row=1, column=0, sticky="ne", padx=5, pady=5)
        
        self.sample_type_desc_var = tk.StringVar()
        sample_type_desc_entry = ModernEntry(
            fields_frame,
            textvariable=self.sample_type_desc_var,
            width=30
        )
        sample_type_desc_entry.grid(row=1, column=1, sticky="w", padx=5, pady=5)
        
        # أزرار العمليات
        buttons_frame = ModernFrame(input_frame, bg=self.colors['surface'])
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        add_btn = ModernButton(
            buttons_frame,
            text="إضافة",
            command=self.add_sample_type,
            bg='#28A745',
            fg='white',
            font=self.font
        )
        add_btn.pack(side=tk.LEFT, padx=5)
        
        update_btn = ModernButton(
            buttons_frame,
            text="تعديل",
            command=self.update_sample_type,
            bg='#FFC107',
            fg='black',
            font=self.font
        )
        update_btn.pack(side=tk.LEFT, padx=5)
        
        delete_btn = ModernButton(
            buttons_frame,
            text="حذف",
            command=self.delete_sample_type,
            bg='#DC3545',
            fg='white',
            font=self.font
        )
        delete_btn.pack(side=tk.LEFT, padx=5)
        
        clear_btn = ModernButton(
            buttons_frame,
            text="مسح",
            command=self.clear_sample_type_form,
            bg='#6C757D',
            fg='white',
            font=self.font
        )
        clear_btn.pack(side=tk.LEFT, padx=5)
        
        # جدول أنواع العينات
        table_frame = ModernLabelFrame(
            tab_frame,
            text="أنواع العينات الموجودة",
            bg=self.colors['surface'],
            fg=self.colors['primary']
        )
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        columns = ("ID", "اسم نوع العينة", "الوصف", "تاريخ الإنشاء")
        self.sample_types_tree = ModernTreeview(table_frame, columns=columns, show='headings', height=8)
        
        for col in columns:
            self.sample_types_tree.heading(col, text=col)
            if col == "ID":
                self.sample_types_tree.column(col, width=50, anchor='center')
            else:
                self.sample_types_tree.column(col, width=150, anchor='center')
                
        # إخفاء عمود ID
        self.sample_types_tree.column("ID", width=0, stretch=False)
        self.sample_types_tree.heading("ID", text="")
        
        sample_types_scrollbar = tk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.sample_types_tree.yview)
        self.sample_types_tree.configure(yscrollcommand=sample_types_scrollbar.set)
        
        self.sample_types_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        sample_types_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.sample_types_tree.bind('<ButtonRelease-1>', self.on_sample_type_select)
        
        # متغيرات للتتبع
        self.selected_sample_type_id = None
        
        # تحميل البيانات
        self.load_sample_types()
        
    def create_organizations_tab(self):
        """إنشاء تبويب جهات الإرسال"""
        tab_frame = ModernFrame(self.notebook, bg=self.colors['surface'])
        self.notebook.add(tab_frame, text="جهات الإرسال")
        
        # إطار الإدخال
        input_frame = ModernLabelFrame(
            tab_frame,
            text="إضافة/تعديل جهة الإرسال",
            bg=self.colors['surface'],
            fg=self.colors['primary']
        )
        input_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # حقول الإدخال
        fields_frame = ModernFrame(input_frame, bg=self.colors['surface'])
        fields_frame.pack(fill=tk.X, padx=10, pady=10)
        
        tk.Label(
            fields_frame,
            text="اسم الجهة:",
            bg=self.colors['surface'],
            fg=self.colors['text'],
            font=self.font
        ).grid(row=0, column=0, sticky="e", padx=5, pady=5)
        
        self.org_name_var = tk.StringVar()
        org_name_entry = ModernEntry(
            fields_frame,
            textvariable=self.org_name_var,
            width=30
        )
        org_name_entry.grid(row=0, column=1, sticky="w", padx=5, pady=5)
        
        tk.Label(
            fields_frame,
            text="العنوان:",
            bg=self.colors['surface'],
            fg=self.colors['text'],
            font=self.font
        ).grid(row=1, column=0, sticky="e", padx=5, pady=5)
        
        self.org_address_var = tk.StringVar()
        org_address_entry = ModernEntry(
            fields_frame,
            textvariable=self.org_address_var,
            width=30
        )
        org_address_entry.grid(row=1, column=1, sticky="w", padx=5, pady=5)
        
        tk.Label(
            fields_frame,
            text="الهاتف:",
            bg=self.colors['surface'],
            fg=self.colors['text'],
            font=self.font
        ).grid(row=2, column=0, sticky="e", padx=5, pady=5)
        
        self.org_phone_var = tk.StringVar()
        org_phone_entry = ModernEntry(
            fields_frame,
            textvariable=self.org_phone_var,
            width=30
        )
        org_phone_entry.grid(row=2, column=1, sticky="w", padx=5, pady=5)
        
        # أزرار العمليات
        buttons_frame = ModernFrame(input_frame, bg=self.colors['surface'])
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        add_btn = ModernButton(
            buttons_frame,
            text="إضافة",
            command=self.add_organization,
            bg='#28A745',
            fg='white',
            font=self.font
        )
        add_btn.pack(side=tk.LEFT, padx=5)
        
        update_btn = ModernButton(
            buttons_frame,
            text="تعديل",
            command=self.update_organization,
            bg='#FFC107',
            fg='black',
            font=self.font
        )
        update_btn.pack(side=tk.LEFT, padx=5)
        
        delete_btn = ModernButton(
            buttons_frame,
            text="حذف",
            command=self.delete_organization,
            bg='#DC3545',
            fg='white',
            font=self.font
        )
        delete_btn.pack(side=tk.LEFT, padx=5)
        
        clear_btn = ModernButton(
            buttons_frame,
            text="مسح",
            command=self.clear_organization_form,
            bg='#6C757D',
            fg='white',
            font=self.font
        )
        clear_btn.pack(side=tk.LEFT, padx=5)
        
        # جدول جهات الإرسال
        table_frame = ModernLabelFrame(
            tab_frame,
            text="جهات الإرسال الموجودة",
            bg=self.colors['surface'],
            fg=self.colors['primary']
        )
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        columns = ("ID", "اسم الجهة", "العنوان", "الهاتف", "تاريخ الإنشاء")
        self.organizations_tree = ModernTreeview(table_frame, columns=columns, show='headings', height=8)
        
        for col in columns:
            self.organizations_tree.heading(col, text=col)
            if col == "ID":
                self.organizations_tree.column(col, width=50, anchor='center')
            else:
                self.organizations_tree.column(col, width=150, anchor='center')
                
        # إخفاء عمود ID
        self.organizations_tree.column("ID", width=0, stretch=False)
        self.organizations_tree.heading("ID", text="")
        
        organizations_scrollbar = tk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.organizations_tree.yview)
        self.organizations_tree.configure(yscrollcommand=organizations_scrollbar.set)
        
        self.organizations_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        organizations_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.organizations_tree.bind('<ButtonRelease-1>', self.on_organization_select)
        
        # متغيرات للتتبع
        self.selected_organization_id = None
        
        # تحميل البيانات
        self.load_organizations()

    def create_tests_tab(self):
        """إنشاء تبويب التحاليل"""
        tab_frame = ModernFrame(self.notebook, bg=self.colors['surface'])
        self.notebook.add(tab_frame, text="التحاليل")

        # إطار الإدخال
        input_frame = ModernLabelFrame(
            tab_frame,
            text="إضافة/تعديل التحليل",
            bg=self.colors['surface'],
            fg=self.colors['primary']
        )
        input_frame.pack(fill=tk.X, padx=10, pady=10)

        # حقول الإدخال
        fields_frame = ModernFrame(input_frame, bg=self.colors['surface'])
        fields_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Label(
            fields_frame,
            text="اسم التحليل:",
            bg=self.colors['surface'],
            fg=self.colors['text'],
            font=self.font
        ).grid(row=0, column=0, sticky="e", padx=5, pady=5)

        self.test_name_var = tk.StringVar()
        test_name_entry = ModernEntry(
            fields_frame,
            textvariable=self.test_name_var,
            width=30
        )
        test_name_entry.grid(row=0, column=1, sticky="w", padx=5, pady=5)

        tk.Label(
            fields_frame,
            text="الوصف:",
            bg=self.colors['surface'],
            fg=self.colors['text'],
            font=self.font
        ).grid(row=1, column=0, sticky="e", padx=5, pady=5)

        self.test_desc_var = tk.StringVar()
        test_desc_entry = ModernEntry(
            fields_frame,
            textvariable=self.test_desc_var,
            width=30
        )
        test_desc_entry.grid(row=1, column=1, sticky="w", padx=5, pady=5)

        # أزرار العمليات
        buttons_frame = ModernFrame(input_frame, bg=self.colors['surface'])
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        add_btn = ModernButton(
            buttons_frame,
            text="إضافة",
            command=self.add_test,
            bg='#28A745',
            fg='white',
            font=self.font
        )
        add_btn.pack(side=tk.LEFT, padx=5)

        update_btn = ModernButton(
            buttons_frame,
            text="تعديل",
            command=self.update_test,
            bg='#FFC107',
            fg='black',
            font=self.font
        )
        update_btn.pack(side=tk.LEFT, padx=5)

        delete_btn = ModernButton(
            buttons_frame,
            text="حذف",
            command=self.delete_test,
            bg='#DC3545',
            fg='white',
            font=self.font
        )
        delete_btn.pack(side=tk.LEFT, padx=5)

        clear_btn = ModernButton(
            buttons_frame,
            text="مسح",
            command=self.clear_test_form,
            bg='#6C757D',
            fg='white',
            font=self.font
        )
        clear_btn.pack(side=tk.LEFT, padx=5)

        # جدول التحاليل
        table_frame = ModernLabelFrame(
            tab_frame,
            text="التحاليل الموجودة",
            bg=self.colors['surface'],
            fg=self.colors['primary']
        )
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        columns = ("ID", "اسم التحليل", "الوصف", "تاريخ الإنشاء")
        self.tests_tree = ModernTreeview(table_frame, columns=columns, show='headings', height=8)

        for col in columns:
            self.tests_tree.heading(col, text=col)
            if col == "ID":
                self.tests_tree.column(col, width=50, anchor='center')
            else:
                self.tests_tree.column(col, width=150, anchor='center')

        # إخفاء عمود ID
        self.tests_tree.column("ID", width=0, stretch=False)
        self.tests_tree.heading("ID", text="")

        tests_scrollbar = tk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tests_tree.yview)
        self.tests_tree.configure(yscrollcommand=tests_scrollbar.set)

        self.tests_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tests_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.tests_tree.bind('<ButtonRelease-1>', self.on_test_select)

        # متغيرات للتتبع
        self.selected_test_id = None

        # تحميل البيانات
        self.load_tests()

    def create_technicians_tab(self):
        """إنشاء تبويب الفنيين"""
        tab_frame = ModernFrame(self.notebook, bg=self.colors['surface'])
        self.notebook.add(tab_frame, text="الفنيين")

        # إطار الإدخال
        input_frame = ModernLabelFrame(
            tab_frame,
            text="إضافة/تعديل الفني",
            bg=self.colors['surface'],
            fg=self.colors['primary']
        )
        input_frame.pack(fill=tk.X, padx=10, pady=10)

        # حقول الإدخال
        fields_frame = ModernFrame(input_frame, bg=self.colors['surface'])
        fields_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Label(
            fields_frame,
            text="اسم الفني:",
            bg=self.colors['surface'],
            fg=self.colors['text'],
            font=self.font
        ).grid(row=0, column=0, sticky="e", padx=5, pady=5)

        self.tech_name_var = tk.StringVar()
        tech_name_entry = ModernEntry(
            fields_frame,
            textvariable=self.tech_name_var,
            width=30
        )
        tech_name_entry.grid(row=0, column=1, sticky="w", padx=5, pady=5)

        tk.Label(
            fields_frame,
            text="التخصص:",
            bg=self.colors['surface'],
            fg=self.colors['text'],
            font=self.font
        ).grid(row=1, column=0, sticky="e", padx=5, pady=5)

        self.tech_spec_var = tk.StringVar()
        tech_spec_entry = ModernEntry(
            fields_frame,
            textvariable=self.tech_spec_var,
            width=30
        )
        tech_spec_entry.grid(row=1, column=1, sticky="w", padx=5, pady=5)

        tk.Label(
            fields_frame,
            text="الهاتف:",
            bg=self.colors['surface'],
            fg=self.colors['text'],
            font=self.font
        ).grid(row=2, column=0, sticky="e", padx=5, pady=5)

        self.tech_phone_var = tk.StringVar()
        tech_phone_entry = ModernEntry(
            fields_frame,
            textvariable=self.tech_phone_var,
            width=30
        )
        tech_phone_entry.grid(row=2, column=1, sticky="w", padx=5, pady=5)

        # أزرار العمليات
        buttons_frame = ModernFrame(input_frame, bg=self.colors['surface'])
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        add_btn = ModernButton(
            buttons_frame,
            text="إضافة",
            command=self.add_technician,
            bg='#28A745',
            fg='white',
            font=self.font
        )
        add_btn.pack(side=tk.LEFT, padx=5)

        update_btn = ModernButton(
            buttons_frame,
            text="تعديل",
            command=self.update_technician,
            bg='#FFC107',
            fg='black',
            font=self.font
        )
        update_btn.pack(side=tk.LEFT, padx=5)

        delete_btn = ModernButton(
            buttons_frame,
            text="حذف",
            command=self.delete_technician,
            bg='#DC3545',
            fg='white',
            font=self.font
        )
        delete_btn.pack(side=tk.LEFT, padx=5)

        clear_btn = ModernButton(
            buttons_frame,
            text="مسح",
            command=self.clear_technician_form,
            bg='#6C757D',
            fg='white',
            font=self.font
        )
        clear_btn.pack(side=tk.LEFT, padx=5)

        # جدول الفنيين
        table_frame = ModernLabelFrame(
            tab_frame,
            text="الفنيين الموجودين",
            bg=self.colors['surface'],
            fg=self.colors['primary']
        )
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        columns = ("ID", "اسم الفني", "التخصص", "الهاتف", "تاريخ الإنشاء")
        self.technicians_tree = ModernTreeview(table_frame, columns=columns, show='headings', height=8)

        for col in columns:
            self.technicians_tree.heading(col, text=col)
            if col == "ID":
                self.technicians_tree.column(col, width=50, anchor='center')
            else:
                self.technicians_tree.column(col, width=150, anchor='center')

        # إخفاء عمود ID
        self.technicians_tree.column("ID", width=0, stretch=False)
        self.technicians_tree.heading("ID", text="")

        technicians_scrollbar = tk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.technicians_tree.yview)
        self.technicians_tree.configure(yscrollcommand=technicians_scrollbar.set)

        self.technicians_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        technicians_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.technicians_tree.bind('<ButtonRelease-1>', self.on_technician_select)

        # متغيرات للتتبع
        self.selected_technician_id = None

        # تحميل البيانات
        self.load_technicians()

    def create_report_settings_tab(self):
        """إنشاء تبويب إعدادات التقارير"""
        tab_frame = ModernFrame(self.notebook, bg=self.colors['surface'])
        self.notebook.add(tab_frame, text="إعدادات التقارير")

        # إطار الإعدادات
        settings_frame = ModernLabelFrame(
            tab_frame,
            text="إعدادات التقرير",
            bg=self.colors['surface'],
            fg=self.colors['primary']
        )
        settings_frame.pack(fill=tk.X, padx=10, pady=10)

        # حقول الإعدادات
        fields_frame = ModernFrame(settings_frame, bg=self.colors['surface'])
        fields_frame.pack(fill=tk.X, padx=10, pady=10)

        # متغيرات الإعدادات
        self.report_settings_vars = {}

        settings_fields = [
            ("header_right", "الجهة اليمنى العليا"),
            ("header_left", "الجهة اليسار العليا"),
            ("footer_address", "العنوان أسفل الصفحة"),
            ("footer_email", "البريد الإلكتروني"),
            ("logo_path", "مسار الشعار")
        ]

        row = 0
        for setting_key, setting_label in settings_fields:
            tk.Label(
                fields_frame,
                text=f"{setting_label}:",
                bg=self.colors['surface'],
                fg=self.colors['text'],
                font=self.font
            ).grid(row=row, column=0, sticky="e", padx=5, pady=5)

            self.report_settings_vars[setting_key] = tk.StringVar()

            if setting_key == "logo_path":
                # إطار خاص لمسار الشعار
                logo_frame = ModernFrame(fields_frame, bg=self.colors['surface'])
                logo_frame.grid(row=row, column=1, sticky="w", padx=5, pady=5)

                logo_entry = ModernEntry(
                    logo_frame,
                    textvariable=self.report_settings_vars[setting_key],
                    width=25
                )
                logo_entry.pack(side=tk.LEFT, padx=(0, 5))

                browse_btn = ModernButton(
                    logo_frame,
                    text="تصفح",
                    command=self.browse_logo,
                    bg=self.colors['primary'],
                    fg='white',
                    font=('Arial', 8)
                )
                browse_btn.pack(side=tk.LEFT)
            else:
                entry = ModernEntry(
                    fields_frame,
                    textvariable=self.report_settings_vars[setting_key],
                    width=30
                )
                entry.grid(row=row, column=1, sticky="w", padx=5, pady=5)

            row += 1

        # أزرار العمليات
        buttons_frame = ModernFrame(settings_frame, bg=self.colors['surface'])
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        save_settings_btn = ModernButton(
            buttons_frame,
            text="حفظ الإعدادات",
            command=self.save_report_settings,
            bg='#28A745',
            fg='white',
            font=self.font
        )
        save_settings_btn.pack(side=tk.LEFT, padx=5)

        load_settings_btn = ModernButton(
            buttons_frame,
            text="تحميل الإعدادات",
            command=self.load_report_settings,
            bg='#17A2B8',
            fg='white',
            font=self.font
        )
        load_settings_btn.pack(side=tk.LEFT, padx=5)

        # تحميل الإعدادات
        self.load_report_settings()

    # وظائف أنواع العينات
    def load_sample_types(self):
        """تحميل أنواع العينات"""
        for item in self.sample_types_tree.get_children():
            self.sample_types_tree.delete(item)

        sample_types = self.db_manager.fetch_all("SELECT * FROM sample_types ORDER BY name")

        for sample_type in sample_types:
            self.sample_types_tree.insert('', tk.END, values=(
                sample_type['id'],
                sample_type['name'],
                sample_type['description'] or '',
                sample_type['created_at']
            ))

    def on_sample_type_select(self, event):
        """عند اختيار نوع عينة"""
        selection = self.sample_types_tree.selection()
        if selection:
            item = self.sample_types_tree.item(selection[0])
            values = item['values']

            self.selected_sample_type_id = values[0]
            self.sample_type_name_var.set(values[1])
            self.sample_type_desc_var.set(values[2])

    def add_sample_type(self):
        """إضافة نوع عينة جديد"""
        name = self.sample_type_name_var.get().strip()
        if not name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم نوع العينة")
            return

        try:
            query = "INSERT INTO sample_types (name, description) VALUES (?, ?)"
            params = (name, self.sample_type_desc_var.get().strip() or None)

            self.db_manager.execute_query(query, params)
            messagebox.showinfo("نجح", "تم إضافة نوع العينة بنجاح")
            self.load_sample_types()
            self.clear_sample_type_form()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إضافة نوع العينة: {str(e)}")

    def update_sample_type(self):
        """تعديل نوع العينة"""
        if not self.selected_sample_type_id:
            messagebox.showwarning("تحذير", "يرجى اختيار نوع عينة للتعديل")
            return

        name = self.sample_type_name_var.get().strip()
        if not name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم نوع العينة")
            return

        try:
            query = "UPDATE sample_types SET name = ?, description = ? WHERE id = ?"
            params = (name, self.sample_type_desc_var.get().strip() or None, self.selected_sample_type_id)

            self.db_manager.execute_query(query, params)
            messagebox.showinfo("نجح", "تم تعديل نوع العينة بنجاح")
            self.load_sample_types()
            self.clear_sample_type_form()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تعديل نوع العينة: {str(e)}")

    def delete_sample_type(self):
        """حذف نوع العينة"""
        if not self.selected_sample_type_id:
            messagebox.showwarning("تحذير", "يرجى اختيار نوع عينة للحذف")
            return

        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف نوع العينة؟"):
            try:
                query = "DELETE FROM sample_types WHERE id = ?"
                self.db_manager.execute_query(query, (self.selected_sample_type_id,))

                messagebox.showinfo("نجح", "تم حذف نوع العينة بنجاح")
                self.load_sample_types()
                self.clear_sample_type_form()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء حذف نوع العينة: {str(e)}")

    def clear_sample_type_form(self):
        """مسح نموذج نوع العينة"""
        self.sample_type_name_var.set("")
        self.sample_type_desc_var.set("")
        self.selected_sample_type_id = None

    # وظائف جهات الإرسال
    def load_organizations(self):
        """تحميل جهات الإرسال"""
        for item in self.organizations_tree.get_children():
            self.organizations_tree.delete(item)

        organizations = self.db_manager.fetch_all("SELECT * FROM sender_organizations ORDER BY name")

        for org in organizations:
            self.organizations_tree.insert('', tk.END, values=(
                org['id'],
                org['name'],
                org['address'] or '',
                org['phone'] or '',
                org['created_at']
            ))

    def on_organization_select(self, event):
        """عند اختيار جهة إرسال"""
        selection = self.organizations_tree.selection()
        if selection:
            item = self.organizations_tree.item(selection[0])
            values = item['values']

            self.selected_organization_id = values[0]
            self.org_name_var.set(values[1])
            self.org_address_var.set(values[2])
            self.org_phone_var.set(values[3])

    def add_organization(self):
        """إضافة جهة إرسال جديدة"""
        name = self.org_name_var.get().strip()
        if not name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم الجهة")
            return

        try:
            query = "INSERT INTO sender_organizations (name, address, phone) VALUES (?, ?, ?)"
            params = (
                name,
                self.org_address_var.get().strip() or None,
                self.org_phone_var.get().strip() or None
            )

            self.db_manager.execute_query(query, params)
            messagebox.showinfo("نجح", "تم إضافة جهة الإرسال بنجاح")
            self.load_organizations()
            self.clear_organization_form()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إضافة جهة الإرسال: {str(e)}")

    def update_organization(self):
        """تعديل جهة الإرسال"""
        if not self.selected_organization_id:
            messagebox.showwarning("تحذير", "يرجى اختيار جهة إرسال للتعديل")
            return

        name = self.org_name_var.get().strip()
        if not name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم الجهة")
            return

        try:
            query = "UPDATE sender_organizations SET name = ?, address = ?, phone = ? WHERE id = ?"
            params = (
                name,
                self.org_address_var.get().strip() or None,
                self.org_phone_var.get().strip() or None,
                self.selected_organization_id
            )

            self.db_manager.execute_query(query, params)
            messagebox.showinfo("نجح", "تم تعديل جهة الإرسال بنجاح")
            self.load_organizations()
            self.clear_organization_form()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تعديل جهة الإرسال: {str(e)}")

    def delete_organization(self):
        """حذف جهة الإرسال"""
        if not self.selected_organization_id:
            messagebox.showwarning("تحذير", "يرجى اختيار جهة إرسال للحذف")
            return

        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف جهة الإرسال؟"):
            try:
                query = "DELETE FROM sender_organizations WHERE id = ?"
                self.db_manager.execute_query(query, (self.selected_organization_id,))

                messagebox.showinfo("نجح", "تم حذف جهة الإرسال بنجاح")
                self.load_organizations()
                self.clear_organization_form()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء حذف جهة الإرسال: {str(e)}")

    def clear_organization_form(self):
        """مسح نموذج جهة الإرسال"""
        self.org_name_var.set("")
        self.org_address_var.set("")
        self.org_phone_var.set("")
        self.selected_organization_id = None

    # وظائف التحاليل
    def load_tests(self):
        """تحميل التحاليل"""
        for item in self.tests_tree.get_children():
            self.tests_tree.delete(item)

        tests = self.db_manager.fetch_all("SELECT * FROM tests ORDER BY name")

        for test in tests:
            self.tests_tree.insert('', tk.END, values=(
                test['id'],
                test['name'],
                test['description'] or '',
                test['created_at']
            ))

    def on_test_select(self, event):
        """عند اختيار تحليل"""
        selection = self.tests_tree.selection()
        if selection:
            item = self.tests_tree.item(selection[0])
            values = item['values']

            self.selected_test_id = values[0]
            self.test_name_var.set(values[1])
            self.test_desc_var.set(values[2])

    def add_test(self):
        """إضافة تحليل جديد"""
        name = self.test_name_var.get().strip()
        if not name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم التحليل")
            return

        try:
            query = "INSERT INTO tests (name, description) VALUES (?, ?)"
            params = (name, self.test_desc_var.get().strip() or None)

            self.db_manager.execute_query(query, params)
            messagebox.showinfo("نجح", "تم إضافة التحليل بنجاح")
            self.load_tests()
            self.clear_test_form()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إضافة التحليل: {str(e)}")

    def update_test(self):
        """تعديل التحليل"""
        if not self.selected_test_id:
            messagebox.showwarning("تحذير", "يرجى اختيار تحليل للتعديل")
            return

        name = self.test_name_var.get().strip()
        if not name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم التحليل")
            return

        try:
            query = "UPDATE tests SET name = ?, description = ? WHERE id = ?"
            params = (name, self.test_desc_var.get().strip() or None, self.selected_test_id)

            self.db_manager.execute_query(query, params)
            messagebox.showinfo("نجح", "تم تعديل التحليل بنجاح")
            self.load_tests()
            self.clear_test_form()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تعديل التحليل: {str(e)}")

    def delete_test(self):
        """حذف التحليل"""
        if not self.selected_test_id:
            messagebox.showwarning("تحذير", "يرجى اختيار تحليل للحذف")
            return

        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف التحليل؟"):
            try:
                query = "DELETE FROM tests WHERE id = ?"
                self.db_manager.execute_query(query, (self.selected_test_id,))

                messagebox.showinfo("نجح", "تم حذف التحليل بنجاح")
                self.load_tests()
                self.clear_test_form()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء حذف التحليل: {str(e)}")

    def clear_test_form(self):
        """مسح نموذج التحليل"""
        self.test_name_var.set("")
        self.test_desc_var.set("")
        self.selected_test_id = None

    # وظائف الفنيين
    def load_technicians(self):
        """تحميل الفنيين"""
        for item in self.technicians_tree.get_children():
            self.technicians_tree.delete(item)

        technicians = self.db_manager.fetch_all("SELECT * FROM technicians ORDER BY name")

        for tech in technicians:
            self.technicians_tree.insert('', tk.END, values=(
                tech['id'],
                tech['name'],
                tech['specialization'] or '',
                tech['phone'] or '',
                tech['created_at']
            ))

    def on_technician_select(self, event):
        """عند اختيار فني"""
        selection = self.technicians_tree.selection()
        if selection:
            item = self.technicians_tree.item(selection[0])
            values = item['values']

            self.selected_technician_id = values[0]
            self.tech_name_var.set(values[1])
            self.tech_spec_var.set(values[2])
            self.tech_phone_var.set(values[3])

    def add_technician(self):
        """إضافة فني جديد"""
        name = self.tech_name_var.get().strip()
        if not name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم الفني")
            return

        try:
            query = "INSERT INTO technicians (name, specialization, phone) VALUES (?, ?, ?)"
            params = (
                name,
                self.tech_spec_var.get().strip() or None,
                self.tech_phone_var.get().strip() or None
            )

            self.db_manager.execute_query(query, params)
            messagebox.showinfo("نجح", "تم إضافة الفني بنجاح")
            self.load_technicians()
            self.clear_technician_form()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إضافة الفني: {str(e)}")

    def update_technician(self):
        """تعديل الفني"""
        if not self.selected_technician_id:
            messagebox.showwarning("تحذير", "يرجى اختيار فني للتعديل")
            return

        name = self.tech_name_var.get().strip()
        if not name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم الفني")
            return

        try:
            query = "UPDATE technicians SET name = ?, specialization = ?, phone = ? WHERE id = ?"
            params = (
                name,
                self.tech_spec_var.get().strip() or None,
                self.tech_phone_var.get().strip() or None,
                self.selected_technician_id
            )

            self.db_manager.execute_query(query, params)
            messagebox.showinfo("نجح", "تم تعديل الفني بنجاح")
            self.load_technicians()
            self.clear_technician_form()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تعديل الفني: {str(e)}")

    def delete_technician(self):
        """حذف الفني"""
        if not self.selected_technician_id:
            messagebox.showwarning("تحذير", "يرجى اختيار فني للحذف")
            return

        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف الفني؟"):
            try:
                query = "DELETE FROM technicians WHERE id = ?"
                self.db_manager.execute_query(query, (self.selected_technician_id,))

                messagebox.showinfo("نجح", "تم حذف الفني بنجاح")
                self.load_technicians()
                self.clear_technician_form()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء حذف الفني: {str(e)}")

    def clear_technician_form(self):
        """مسح نموذج الفني"""
        self.tech_name_var.set("")
        self.tech_spec_var.set("")
        self.tech_phone_var.set("")
        self.selected_technician_id = None

    # وظائف إعدادات التقارير
    def browse_logo(self):
        """تصفح ملف الشعار"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف الشعار",
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"), ("All files", "*.*")]
        )

        if file_path:
            self.report_settings_vars['logo_path'].set(file_path)

    def save_report_settings(self):
        """حفظ إعدادات التقارير"""
        try:
            for setting_name, var in self.report_settings_vars.items():
                value = var.get().strip()

                # تحديث أو إدراج الإعداد
                existing = self.db_manager.fetch_one(
                    "SELECT id FROM report_settings WHERE setting_name = ?",
                    (setting_name,)
                )

                if existing:
                    query = "UPDATE report_settings SET setting_value = ?, updated_at = CURRENT_TIMESTAMP WHERE setting_name = ?"
                    params = (value, setting_name)
                else:
                    query = "INSERT INTO report_settings (setting_name, setting_value) VALUES (?, ?)"
                    params = (setting_name, value)

                self.db_manager.execute_query(query, params)

            messagebox.showinfo("نجح", "تم حفظ إعدادات التقارير بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ الإعدادات: {str(e)}")

    def load_report_settings(self):
        """تحميل إعدادات التقارير"""
        try:
            settings = self.db_manager.fetch_all("SELECT setting_name, setting_value FROM report_settings")

            # مسح القيم الحالية
            for var in self.report_settings_vars.values():
                var.set("")

            # تحميل القيم المحفوظة
            for setting in settings:
                setting_name = setting['setting_name']
                setting_value = setting['setting_value'] or ''

                if setting_name in self.report_settings_vars:
                    self.report_settings_vars[setting_name].set(setting_value)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل الإعدادات: {str(e)}")

    def show(self):
        """عرض التبويب"""
        if self.frame:
            self.frame.pack(fill=tk.BOTH, expand=True)

    def hide(self):
        """إخفاء التبويب"""
        if self.frame:
            self.frame.pack_forget()
