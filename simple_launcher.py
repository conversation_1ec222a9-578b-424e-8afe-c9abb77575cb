#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل مبسط للتطبيق
Simple Application Launcher
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def create_simple_app():
    """إنشاء تطبيق مبسط للاختبار"""
    root = tk.Tk()
    root.title("مختبر الصحة العامة المركزي - ذي قار")
    root.geometry("800x600")
    root.configure(bg='#F8F9FA')
    
    # الإطار الرئيسي
    main_frame = tk.Frame(root, bg='#FFFFFF', relief=tk.RAISED, bd=2)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    
    # العنوان
    title_label = tk.Label(
        main_frame,
        text="مختبر الصحة العامة المركزي - ذي قار",
        font=('Arial', 18, 'bold'),
        fg='#2E86AB',
        bg='#FFFFFF'
    )
    title_label.pack(pady=20)
    
    # رسالة الحالة
    status_label = tk.Label(
        main_frame,
        text="التطبيق يعمل بنجاح! 🎉",
        font=('Arial', 14, 'bold'),
        fg='#28A745',
        bg='#FFFFFF'
    )
    status_label.pack(pady=10)
    
    # معلومات النظام
    info_text = f"""
معلومات النظام:
• إصدار Python: {sys.version.split()[0]}
• نظام التشغيل: {os.name}
• مجلد العمل: {os.getcwd()}

الملفات الموجودة:
"""
    
    # قائمة الملفات
    files = [f for f in os.listdir('.') if f.endswith('.py')]
    for file in files[:10]:  # أول 10 ملفات
        info_text += f"• {file}\n"
    
    info_label = tk.Label(
        main_frame,
        text=info_text,
        font=('Arial', 10),
        fg='#212529',
        bg='#FFFFFF',
        justify=tk.LEFT
    )
    info_label.pack(pady=20)
    
    # أزرار التحكم
    buttons_frame = tk.Frame(main_frame, bg='#FFFFFF')
    buttons_frame.pack(pady=20)
    
    def launch_main():
        """تشغيل التطبيق الرئيسي"""
        try:
            root.destroy()
            import main
            app = main.LabManagementSystem()
            app.run()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تشغيل التطبيق الرئيسي:\n{str(e)}")
    
    def test_database():
        """اختبار قاعدة البيانات"""
        try:
            from database import DatabaseManager
            db = DatabaseManager("test.db")
            messagebox.showinfo("نجح", "قاعدة البيانات تعمل بشكل صحيح!")
            if os.path.exists("test.db"):
                os.remove("test.db")
        except Exception as e:
            messagebox.showerror("خطأ", f"مشكلة في قاعدة البيانات:\n{str(e)}")
    
    def show_files():
        """عرض الملفات"""
        files_window = tk.Toplevel(root)
        files_window.title("ملفات التطبيق")
        files_window.geometry("400x300")
        
        text_widget = tk.Text(files_window, wrap=tk.WORD)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        all_files = os.listdir('.')
        for file in sorted(all_files):
            text_widget.insert(tk.END, f"{file}\n")
        
        text_widget.configure(state='disabled')
    
    # الأزرار
    tk.Button(
        buttons_frame,
        text="تشغيل التطبيق الكامل",
        command=launch_main,
        bg='#28A745',
        fg='white',
        font=('Arial', 12, 'bold'),
        width=20,
        height=2
    ).pack(side=tk.LEFT, padx=10)
    
    tk.Button(
        buttons_frame,
        text="اختبار قاعدة البيانات",
        command=test_database,
        bg='#17A2B8',
        fg='white',
        font=('Arial', 12, 'bold'),
        width=20,
        height=2
    ).pack(side=tk.LEFT, padx=10)
    
    tk.Button(
        buttons_frame,
        text="عرض الملفات",
        command=show_files,
        bg='#FFC107',
        fg='black',
        font=('Arial', 12, 'bold'),
        width=20,
        height=2
    ).pack(side=tk.LEFT, padx=10)
    
    # زر الإغلاق
    tk.Button(
        main_frame,
        text="إغلاق",
        command=root.quit,
        bg='#DC3545',
        fg='white',
        font=('Arial', 12, 'bold'),
        width=15,
        height=2
    ).pack(side=tk.BOTTOM, pady=20)
    
    root.mainloop()

if __name__ == "__main__":
    print("========================================")
    print("  مختبر الصحة العامة المركزي - ذي قار")
    print("  تشغيل التطبيق...")
    print("========================================")
    
    try:
        create_simple_app()
    except Exception as e:
        print(f"خطأ: {e}")
        input("اضغط Enter للخروج...")
