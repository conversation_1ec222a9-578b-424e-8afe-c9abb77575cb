#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للتطبيق
Simple Application Test
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_basic_functionality():
    """اختبار الوظائف الأساسية"""
    try:
        # اختبار استيراد المكتبات الأساسية
        import sqlite3
        import datetime
        from pathlib import Path
        
        print("✓ المكتبات الأساسية متوفرة")
        
        # اختبار إنشاء قاعدة البيانات
        from database import DatabaseManager
        db = DatabaseManager("test_database.db")
        print("✓ قاعدة البيانات تعمل بشكل صحيح")
        
        # اختبار المكونات
        from ui_components import ModernButton, ModernFrame
        print("✓ مكونات واجهة المستخدم متوفرة")
        
        # اختبار النوافذ
        root = tk.Tk()
        root.title("اختبار التطبيق")
        root.geometry("400x300")
        
        frame = ModernFrame(root, bg='white')
        frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        title_label = tk.Label(
            frame,
            text="مختبر الصحة العامة المركزي - ذي قار",
            font=('Arial', 14, 'bold'),
            bg='white',
            fg='#2E86AB'
        )
        title_label.pack(pady=20)
        
        test_label = tk.Label(
            frame,
            text="اختبار التطبيق نجح!",
            font=('Arial', 12),
            bg='white',
            fg='green'
        )
        test_label.pack(pady=10)
        
        def close_test():
            root.destroy()
            
        close_btn = ModernButton(
            frame,
            text="إغلاق",
            command=close_test,
            bg='#DC3545',
            fg='white'
        )
        close_btn.pack(pady=20)
        
        # عرض النافذة
        root.mainloop()
        
        # تنظيف ملف الاختبار
        if os.path.exists("test_database.db"):
            os.remove("test_database.db")
            
        return True
        
    except Exception as e:
        print(f"✗ خطأ في الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("========================================")
    print("  اختبار تطبيق مختبر الصحة العامة")
    print("  Testing Lab Management Application")
    print("========================================")
    print()
    
    print("بدء الاختبار...")
    
    if test_basic_functionality():
        print("\n✓ جميع الاختبارات نجحت!")
        print("يمكنك الآن تشغيل التطبيق الكامل")
    else:
        print("\n✗ فشل في بعض الاختبارات")
        print("يرجى التحقق من تثبيت المكتبات المطلوبة")
        
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
