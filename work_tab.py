#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تبويب العمل
Work Tab
"""

import tkinter as tk
from tkinter import ttk, messagebox
import datetime
from ui_components import *

class WorkTab:
    def __init__(self, parent, db_manager, colors, font):
        self.parent = parent
        self.db_manager = db_manager
        self.colors = colors
        self.font = font
        self.frame = None
        self.current_batch_id = None
        self.create_ui()
        
    def create_ui(self):
        """إنشاء واجهة العمل"""
        self.frame = ModernFrame(self.parent, bg=self.colors['surface'])
        
        # عنوان التبويب
        title_label = ModernLabel(
            self.frame,
            text="إدارة الوجبات والعمل",
            font=('Arial', 16, 'bold'),
            fg=self.colors['primary'],
            bg=self.colors['surface']
        )
        title_label.pack(pady=20)
        
        # إنشاء الإطار الرئيسي
        main_frame = ModernFrame(self.frame, bg=self.colors['surface'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20)
        
        # إنشاء نموذج إنشاء الوجبة
        self.create_batch_form(main_frame)
        
        # إنشاء جدول الوجبات
        self.create_batches_table(main_frame)
        
        # إنشاء منطقة تفاصيل الوجبة
        self.create_batch_details(main_frame)
        
        # تحديث البيانات
        self.refresh_batches()
        
    def create_batch_form(self, parent):
        """إنشاء نموذج إنشاء الوجبة"""
        form_frame = ModernLabelFrame(
            parent,
            text="إنشاء وجبة جديدة",
            bg=self.colors['surface'],
            fg=self.colors['primary']
        )
        form_frame.pack(fill=tk.X, pady=10)
        
        # إطار الحقول
        fields_frame = ModernFrame(form_frame, bg=self.colors['surface'])
        fields_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # متغيرات النموذج
        self.batch_vars = {}
        
        # تاريخ البداية
        tk.Label(
            fields_frame,
            text="تاريخ البداية:",
            bg=self.colors['surface'],
            fg=self.colors['text'],
            font=self.font
        ).grid(row=0, column=0, sticky="e", padx=10, pady=5)
        
        self.batch_vars['start_date'] = tk.StringVar()
        start_date_entry = ModernEntry(
            fields_frame,
            textvariable=self.batch_vars['start_date'],
            width=15
        )
        start_date_entry.grid(row=0, column=1, sticky="w", padx=10, pady=5)
        
        # تاريخ النهاية
        tk.Label(
            fields_frame,
            text="تاريخ النهاية:",
            bg=self.colors['surface'],
            fg=self.colors['text'],
            font=self.font
        ).grid(row=0, column=2, sticky="e", padx=10, pady=5)
        
        self.batch_vars['end_date'] = tk.StringVar()
        end_date_entry = ModernEntry(
            fields_frame,
            textvariable=self.batch_vars['end_date'],
            width=15
        )
        end_date_entry.grid(row=0, column=3, sticky="w", padx=10, pady=5)
        
        # رقم الوجبة (تلقائي)
        tk.Label(
            fields_frame,
            text="رقم الوجبة:",
            bg=self.colors['surface'],
            fg=self.colors['text_secondary'],
            font=self.font
        ).grid(row=1, column=0, sticky="e", padx=10, pady=5)
        
        self.batch_vars['batch_number'] = tk.StringVar()
        batch_number_entry = ModernEntry(
            fields_frame,
            textvariable=self.batch_vars['batch_number'],
            width=15,
            state='readonly'
        )
        batch_number_entry.grid(row=1, column=1, sticky="w", padx=10, pady=5)
        
        # تاريخ العمل (تلقائي)
        tk.Label(
            fields_frame,
            text="تاريخ العمل:",
            bg=self.colors['surface'],
            fg=self.colors['text_secondary'],
            font=self.font
        ).grid(row=1, column=2, sticky="e", padx=10, pady=5)
        
        self.batch_vars['work_date'] = tk.StringVar(
            value=datetime.date.today().strftime("%Y-%m-%d")
        )
        work_date_entry = ModernEntry(
            fields_frame,
            textvariable=self.batch_vars['work_date'],
            width=15,
            state='readonly'
        )
        work_date_entry.grid(row=1, column=3, sticky="w", padx=10, pady=5)
        
        # أزرار العمليات
        buttons_frame = ModernFrame(form_frame, bg=self.colors['surface'])
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # زر إنشاء الوجبة
        create_btn = ModernButton(
            buttons_frame,
            text="إنشاء الوجبة",
            command=self.create_batch,
            bg='#28A745',
            fg='white',
            font=self.font
        )
        create_btn.pack(side=tk.LEFT, padx=5)
        
        # زر تحديث الوجبة
        update_btn = ModernButton(
            buttons_frame,
            text="تحديث الوجبة",
            command=self.update_batch,
            bg='#FFC107',
            fg='black',
            font=self.font
        )
        update_btn.pack(side=tk.LEFT, padx=5)
        
        # زر حذف الوجبة
        delete_btn = ModernButton(
            buttons_frame,
            text="حذف الوجبة",
            command=self.delete_batch,
            bg='#DC3545',
            fg='white',
            font=self.font
        )
        delete_btn.pack(side=tk.LEFT, padx=5)
        
        # زر مسح النموذج
        clear_btn = ModernButton(
            buttons_frame,
            text="مسح النموذج",
            command=self.clear_batch_form,
            bg='#6C757D',
            fg='white',
            font=self.font
        )
        clear_btn.pack(side=tk.LEFT, padx=5)
        
    def create_batches_table(self, parent):
        """إنشاء جدول الوجبات"""
        table_frame = ModernLabelFrame(
            parent,
            text="الوجبات",
            bg=self.colors['surface'],
            fg=self.colors['primary']
        )
        table_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # إنشاء الجدول
        columns = ("رقم الوجبة", "تاريخ البداية", "تاريخ النهاية", "تاريخ العمل", "عدد العينات", "الحالة")
        self.batches_tree = ModernTreeview(table_frame, columns=columns, show='headings', height=8)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.batches_tree.heading(col, text=col)
            self.batches_tree.column(col, width=120, anchor='center')
            
        # شريط التمرير
        batches_scrollbar = tk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.batches_tree.yview)
        self.batches_tree.configure(yscrollcommand=batches_scrollbar.set)
        
        self.batches_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        batches_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط حدث النقر
        self.batches_tree.bind('<ButtonRelease-1>', self.on_batch_select)
        
    def create_batch_details(self, parent):
        """إنشاء منطقة تفاصيل الوجبة"""
        details_frame = ModernLabelFrame(
            parent,
            text="تفاصيل الوجبة والعينات",
            bg=self.colors['surface'],
            fg=self.colors['primary']
        )
        details_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # جدول العينات في الوجبة
        samples_columns = ("الرقم الوطني", "الاسم", "نوع العينة", "التحاليل", "الفني المسؤول")
        self.samples_tree = ModernTreeview(details_frame, columns=samples_columns, show='headings', height=8)
        
        for col in samples_columns:
            self.samples_tree.heading(col, text=col)
            self.samples_tree.column(col, width=150, anchor='center')
            
        # شريط التمرير للعينات
        samples_scrollbar = tk.Scrollbar(details_frame, orient=tk.VERTICAL, command=self.samples_tree.yview)
        self.samples_tree.configure(yscrollcommand=samples_scrollbar.set)
        
        self.samples_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        samples_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # إطار تعيين الفنيين
        technicians_frame = ModernFrame(details_frame, bg=self.colors['surface'])
        technicians_frame.pack(fill=tk.X, pady=10)
        
        # قائمة الفنيين
        tk.Label(
            technicians_frame,
            text="الفني المسؤول:",
            bg=self.colors['surface'],
            fg=self.colors['text'],
            font=self.font
        ).pack(side=tk.LEFT, padx=10)
        
        self.technician_var = tk.StringVar()
        self.technician_combo = ModernCombobox(
            technicians_frame,
            textvariable=self.technician_var,
            width=20
        )
        self.technician_combo.pack(side=tk.LEFT, padx=10)
        self.load_technicians()
        
        # زر تعيين الفني
        assign_btn = ModernButton(
            technicians_frame,
            text="تعيين الفني",
            command=self.assign_technician,
            bg=self.colors['primary'],
            fg='white',
            font=self.font
        )
        assign_btn.pack(side=tk.LEFT, padx=10)
        
        # زر إرسال الوجبة للنتائج
        send_btn = ModernButton(
            technicians_frame,
            text="إرسال للنتائج",
            command=self.send_to_results,
            bg='#28A745',
            fg='white',
            font=self.font
        )
        send_btn.pack(side=tk.RIGHT, padx=10)
        
    def load_technicians(self):
        """تحميل قائمة الفنيين"""
        technicians = self.db_manager.fetch_all("SELECT name FROM technicians ORDER BY name")
        self.technician_combo['values'] = [tech['name'] for tech in technicians]
        
    def create_batch(self):
        """إنشاء وجبة جديدة"""
        start_date = self.batch_vars['start_date'].get()
        end_date = self.batch_vars['end_date'].get()
        
        if not start_date or not end_date:
            messagebox.showerror("خطأ", "يرجى إدخال تاريخ البداية والنهاية")
            return
            
        try:
            # التحقق من صحة التواريخ
            start_dt = datetime.datetime.strptime(start_date, "%Y-%m-%d").date()
            end_dt = datetime.datetime.strptime(end_date, "%Y-%m-%d").date()
            
            if start_dt > end_dt:
                messagebox.showerror("خطأ", "تاريخ البداية يجب أن يكون قبل تاريخ النهاية")
                return
                
            # الحصول على رقم الوجبة التالي
            batch_number = self.db_manager.get_next_counter('batch_number')
            self.batch_vars['batch_number'].set(str(batch_number))
            
            # إنشاء الوجبة
            query = '''
                INSERT INTO batches (batch_number, start_date, end_date, work_date, status)
                VALUES (?, ?, ?, ?, 'pending')
            '''
            
            params = (
                batch_number,
                start_date,
                end_date,
                self.batch_vars['work_date'].get()
            )
            
            cursor = self.db_manager.execute_query(query, params)
            if cursor:
                messagebox.showinfo("نجح", f"تم إنشاء الوجبة رقم {batch_number} بنجاح")
                self.refresh_batches()
                self.clear_batch_form()
                
        except ValueError:
            messagebox.showerror("خطأ", "تنسيق التاريخ غير صحيح (YYYY-MM-DD)")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء الوجبة: {str(e)}")
            
    def update_batch(self):
        """تحديث الوجبة"""
        if not self.current_batch_id:
            messagebox.showwarning("تحذير", "يرجى اختيار وجبة للتحديث")
            return
            
        start_date = self.batch_vars['start_date'].get()
        end_date = self.batch_vars['end_date'].get()
        
        if not start_date or not end_date:
            messagebox.showerror("خطأ", "يرجى إدخال تاريخ البداية والنهاية")
            return
            
        try:
            query = '''
                UPDATE batches SET
                    start_date = ?, end_date = ?, work_date = ?
                WHERE id = ?
            '''
            
            params = (
                start_date,
                end_date,
                self.batch_vars['work_date'].get(),
                self.current_batch_id
            )
            
            self.db_manager.execute_query(query, params)
            messagebox.showinfo("نجح", "تم تحديث الوجبة بنجاح")
            self.refresh_batches()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحديث الوجبة: {str(e)}")
            
    def delete_batch(self):
        """حذف الوجبة"""
        if not self.current_batch_id:
            messagebox.showwarning("تحذير", "يرجى اختيار وجبة للحذف")
            return
            
        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذه الوجبة؟"):
            try:
                # حذف الوجبة
                self.db_manager.execute_query(
                    "DELETE FROM batches WHERE id = ?",
                    (self.current_batch_id,)
                )
                
                messagebox.showinfo("نجح", "تم حذف الوجبة بنجاح")
                self.refresh_batches()
                self.clear_batch_form()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء حذف الوجبة: {str(e)}")
                
    def clear_batch_form(self):
        """مسح نموذج الوجبة"""
        for var in self.batch_vars.values():
            var.set("")
            
        self.batch_vars['work_date'].set(datetime.date.today().strftime("%Y-%m-%d"))
        self.current_batch_id = None
        
        # مسح جدول العينات
        for item in self.samples_tree.get_children():
            self.samples_tree.delete(item)
            
    def refresh_batches(self):
        """تحديث جدول الوجبات"""
        # مسح الجدول
        for item in self.batches_tree.get_children():
            self.batches_tree.delete(item)
            
        # جلب البيانات
        query = '''
            SELECT b.*, 
                   COUNT(pt.id) as sample_count
            FROM batches b
            LEFT JOIN patient_tests pt ON b.id = pt.batch_id
            GROUP BY b.id
            ORDER BY b.batch_number DESC
        '''
        
        batches = self.db_manager.fetch_all(query)
        
        # إدراج البيانات
        for batch in batches:
            status_text = {
                'pending': 'في الانتظار',
                'in_progress': 'قيد العمل',
                'completed': 'مكتمل',
                'sent': 'مرسل للنتائج'
            }.get(batch['status'], batch['status'])
            
            self.batches_tree.insert('', tk.END, values=(
                batch['batch_number'],
                batch['start_date'],
                batch['end_date'],
                batch['work_date'],
                batch['sample_count'],
                status_text
            ), tags=(batch['id'],))
            
    def on_batch_select(self, event):
        """عند اختيار وجبة"""
        selection = self.batches_tree.selection()
        if selection:
            item = self.batches_tree.item(selection[0])
            batch_id = item['tags'][0]
            self.load_batch_details(batch_id)
            
    def load_batch_details(self, batch_id):
        """تحميل تفاصيل الوجبة"""
        self.current_batch_id = batch_id
        
        # تحميل بيانات الوجبة
        batch = self.db_manager.fetch_one(
            "SELECT * FROM batches WHERE id = ?",
            (batch_id,)
        )
        
        if batch:
            self.batch_vars['batch_number'].set(str(batch['batch_number']))
            self.batch_vars['start_date'].set(batch['start_date'])
            self.batch_vars['end_date'].set(batch['end_date'])
            self.batch_vars['work_date'].set(batch['work_date'])
            
        # تحميل العينات في الوجبة
        self.load_batch_samples(batch_id)
        
    def load_batch_samples(self, batch_id):
        """تحميل العينات في الوجبة"""
        # مسح الجدول
        for item in self.samples_tree.get_children():
            self.samples_tree.delete(item)
            
        # جلب العينات
        query = '''
            SELECT p.national_id, p.name, p.sample_type,
                   GROUP_CONCAT(t.name) as tests,
                   pt.technician_name
            FROM patients p
            JOIN patient_tests pt ON p.id = pt.patient_id
            JOIN tests t ON pt.test_id = t.id
            WHERE pt.batch_id = ?
            GROUP BY p.id
        '''
        
        samples = self.db_manager.fetch_all(query, (batch_id,))
        
        for sample in samples:
            self.samples_tree.insert('', tk.END, values=(
                sample['national_id'],
                sample['name'],
                sample['sample_type'],
                sample['tests'] or '',
                sample['technician_name'] or ''
            ))
            
    def assign_technician(self):
        """تعيين الفني للعينات المحددة"""
        if not self.current_batch_id:
            messagebox.showwarning("تحذير", "يرجى اختيار وجبة أولاً")
            return
            
        technician_name = self.technician_var.get()
        if not technician_name:
            messagebox.showwarning("تحذير", "يرجى اختيار فني")
            return
            
        try:
            # تحديث جميع العينات في الوجبة
            query = '''
                UPDATE patient_tests 
                SET technician_name = ?
                WHERE batch_id = ?
            '''
            
            self.db_manager.execute_query(query, (technician_name, self.current_batch_id))
            
            # تحديث حالة الوجبة
            self.db_manager.execute_query(
                "UPDATE batches SET status = 'in_progress' WHERE id = ?",
                (self.current_batch_id,)
            )
            
            messagebox.showinfo("نجح", "تم تعيين الفني بنجاح")
            self.refresh_batches()
            self.load_batch_samples(self.current_batch_id)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تعيين الفني: {str(e)}")
            
    def send_to_results(self):
        """إرسال الوجبة إلى تبويب النتائج"""
        if not self.current_batch_id:
            messagebox.showwarning("تحذير", "يرجى اختيار وجبة أولاً")
            return
            
        try:
            # تحديث حالة الوجبة
            self.db_manager.execute_query(
                "UPDATE batches SET status = 'sent' WHERE id = ?",
                (self.current_batch_id,)
            )
            
            messagebox.showinfo("نجح", "تم إرسال الوجبة إلى تبويب النتائج")
            self.refresh_batches()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إرسال الوجبة: {str(e)}")
            
    def show(self):
        """عرض التبويب"""
        if self.frame:
            self.frame.pack(fill=tk.BOTH, expand=True)
            
    def hide(self):
        """إخفاء التبويب"""
        if self.frame:
            self.frame.pack_forget()
