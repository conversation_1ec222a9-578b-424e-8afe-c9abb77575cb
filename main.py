#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مختبر الصحة العامة المركزي ذي قار
Central Public Health Laboratory - Dhi Qar
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sqlite3
import datetime
from pathlib import Path
import os
import sys

# إضافة مسار المجلد الحالي لاستيراد الوحدات
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from config import get_config
    from database import DatabaseManager
    from ui_components import ModernButton, ModernFrame, ModernEntry, ModernCombobox
    from data_entry import DataEntryTab
    from work_tab import WorkTab
    from results_tab import ResultsTab
    from reports_tab import ReportsTab
    from settings_tab import SettingsTab
    from printer_manager import PrinterManager
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    print("تأكد من وجود جميع ملفات التطبيق")
    input("اضغط Enter للخروج...")
    sys.exit(1)

class LabManagementSystem:
    def __init__(self):
        self.root = tk.Tk()
        self.config = get_config()
        self.setup_main_window()
        self.db_manager = DatabaseManager()
        self.printer_manager = PrinterManager()
        self.setup_ui()
        self.setup_tabs()
        
    def setup_main_window(self):
        """إعداد النافذة الرئيسية"""
        ui_config = self.config.get('ui', {})

        self.root.title(ui_config.get('window_title', "مختبر الصحة العامة المركزي - ذي قار"))
        self.root.geometry(ui_config.get('window_size', "1400x800"))

        min_size = ui_config.get('min_window_size', "1200x700").split('x')
        self.root.minsize(int(min_size[0]), int(min_size[1]))

        # تعيين الألوان من التكوين
        self.colors = ui_config.get('theme', {
            'primary': '#2E86AB',
            'secondary': '#A23B72',
            'accent': '#F18F01',
            'background': '#F8F9FA',
            'surface': '#FFFFFF',
            'text': '#212529',
            'text_secondary': '#6C757D'
        })

        self.root.configure(bg=self.colors['background'])

        # تعيين الخط الافتراضي من التكوين
        fonts = ui_config.get('fonts', {})
        self.default_font = fonts.get('default', ('Arial', 10, 'bold'))
        
        # تعيين أيقونة التطبيق (إذا كانت متوفرة)
        try:
            self.root.iconbitmap('assets/icon.ico')
        except:
            pass
            
    def setup_ui(self):
        """إعداد واجهة المستخدم الأساسية"""
        # إنشاء الإطار الرئيسي
        self.main_frame = tk.Frame(self.root, bg=self.colors['background'])
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # إنشاء شريط العنوان
        self.create_header()
        
        # إنشاء الإطار الأساسي للمحتوى
        self.content_frame = tk.Frame(self.main_frame, bg=self.colors['background'])
        self.content_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # إنشاء التبويبات الجانبية
        self.create_sidebar()
        
        # إنشاء منطقة المحتوى الرئيسي
        self.create_main_content()
        
    def create_header(self):
        """إنشاء شريط العنوان"""
        header_frame = ModernFrame(self.main_frame, bg=self.colors['primary'], height=80)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        header_frame.pack_propagate(False)
        
        # عنوان التطبيق
        title_label = tk.Label(
            header_frame,
            text="مختبر الصحة العامة المركزي - ذي قار",
            font=('Arial', 18, 'bold'),
            fg='white',
            bg=self.colors['primary']
        )
        title_label.pack(expand=True)
        
        # التاريخ والوقت
        self.datetime_label = tk.Label(
            header_frame,
            text="",
            font=('Arial', 10),
            fg='white',
            bg=self.colors['primary']
        )
        self.datetime_label.pack(side=tk.BOTTOM, pady=(0, 10))
        self.update_datetime()
        
    def create_sidebar(self):
        """إنشاء التبويبات الجانبية"""
        self.sidebar_frame = ModernFrame(
            self.content_frame, 
            bg=self.colors['surface'], 
            width=200,
            relief=tk.RAISED,
            bd=1
        )
        self.sidebar_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        self.sidebar_frame.pack_propagate(False)
        
        # عنوان التبويبات
        sidebar_title = tk.Label(
            self.sidebar_frame,
            text="التبويبات",
            font=('Arial', 12, 'bold'),
            bg=self.colors['surface'],
            fg=self.colors['text']
        )
        sidebar_title.pack(pady=20)
        
        # أزرار التبويبات
        self.tab_buttons = {}
        tabs = [
            ("إدخال البيانات", "data_entry"),
            ("العمل", "work"),
            ("النتائج", "results"),
            ("التقارير والإحصائيات", "reports"),
            ("الإعدادات", "settings")
        ]
        
        for tab_name, tab_id in tabs:
            btn = ModernButton(
                self.sidebar_frame,
                text=tab_name,
                command=lambda t=tab_id: self.switch_tab(t),
                bg=self.colors['primary'],
                fg='white',
                font=self.default_font,
                width=18,
                height=2
            )
            btn.pack(pady=5, padx=10, fill=tk.X)
            self.tab_buttons[tab_id] = btn
            
    def create_main_content(self):
        """إنشاء منطقة المحتوى الرئيسي"""
        self.main_content_frame = ModernFrame(
            self.content_frame,
            bg=self.colors['surface'],
            relief=tk.RAISED,
            bd=1
        )
        self.main_content_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
    def setup_tabs(self):
        """إعداد التبويبات"""
        self.tabs = {}
        
        # تبويب إدخال البيانات
        self.tabs['data_entry'] = DataEntryTab(
            self.main_content_frame, 
            self.db_manager, 
            self.printer_manager,
            self.colors,
            self.default_font
        )
        
        # تبويب العمل
        self.tabs['work'] = WorkTab(
            self.main_content_frame,
            self.db_manager,
            self.colors,
            self.default_font
        )
        
        # تبويب النتائج
        self.tabs['results'] = ResultsTab(
            self.main_content_frame,
            self.db_manager,
            self.colors,
            self.default_font
        )
        
        # تبويب التقارير
        self.tabs['reports'] = ReportsTab(
            self.main_content_frame,
            self.db_manager,
            self.colors,
            self.default_font
        )
        
        # تبويب الإعدادات
        self.tabs['settings'] = SettingsTab(
            self.main_content_frame,
            self.db_manager,
            self.colors,
            self.default_font
        )
        
        # عرض التبويب الأول افتراضياً
        self.current_tab = None
        self.switch_tab('data_entry')
        
    def switch_tab(self, tab_id):
        """تبديل التبويبات"""
        if self.current_tab == tab_id:
            return
            
        # إخفاء التبويب الحالي
        if self.current_tab and self.current_tab in self.tabs:
            self.tabs[self.current_tab].hide()
            
        # إعادة تعيين ألوان الأزرار
        for btn_id, btn in self.tab_buttons.items():
            if btn_id == tab_id:
                btn.configure(bg=self.colors['accent'])
            else:
                btn.configure(bg=self.colors['primary'])
                
        # عرض التبويب الجديد
        if tab_id in self.tabs:
            self.tabs[tab_id].show()
            self.current_tab = tab_id
            
    def update_datetime(self):
        """تحديث التاريخ والوقت"""
        now = datetime.datetime.now()
        datetime_str = now.strftime("%Y-%m-%d %H:%M:%S")
        self.datetime_label.configure(text=datetime_str)
        self.root.after(1000, self.update_datetime)
        
    def run(self):
        """تشغيل التطبيق"""
        try:
            self.root.mainloop()
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في التطبيق: {str(e)}")
        finally:
            if hasattr(self, 'db_manager'):
                self.db_manager.close()

if __name__ == "__main__":
    app = LabManagementSystem()
    app.run()
