# مختبر الصحة العامة المركزي - ذي قار
## Central Public Health Laboratory - Dhi Qar

تطبيق إدارة مختبر شامل مصمم خصيصاً لمختبر الصحة العامة المركزي في ذي قار.

## المميزات الرئيسية

### 📋 إدخال البيانات
- إدخال بيانات المرضى والعينات مع التحقق من صحة البيانات
- إضافة وتعديل وحذف البيانات
- طباعة الستيكر التلقائي بعد إدخال البيانات
- استيراد البيانات من ملفات Excel
- البحث المتقدم في البيانات

### 🔬 إدارة العمل
- إنشاء وإدارة الوجبات (Batches)
- تحديد فترات العمل
- تعيين الفنيين للوجبات
- إرسال الوجبات إلى تبويب النتائج

### 📊 النتائج
- إدخال النتائج حسب رقم الوجبة
- نتائج متعددة: Negative, Positive, Retest, Recollection, Sent, TND
- أزرار النتائج السريعة
- حفظ النتائج بشكل فردي أو جماعي

### 📈 التقارير والإحصائيات
- تقارير مفردة وجماعية
- فلترة متقدمة حسب التاريخ والاسم ونوع العينة والجنس
- إحصائيات شاملة
- تصدير البيانات إلى Excel
- طباعة التقارير مع شعار الوزارة

### ⚙️ الإعدادات
- إدارة أنواع العينات
- إدارة جهات الإرسال
- إدارة التحاليل
- إدارة الفنيين
- إعدادات التقارير والشعار

## متطلبات النظام

- Windows 10 أو أحدث
- Python 3.8 أو أحدث
- طابعة متصلة بالنظام (للستيكر والتقارير)

## التثبيت والتشغيل

### 1. تثبيت Python
قم بتحميل وتثبيت Python من الموقع الرسمي:
```
https://www.python.org/downloads/
```

### 2. تثبيت المكتبات المطلوبة
افتح Command Prompt وقم بتشغيل الأمر التالي:
```bash
pip install -r requirements.txt
```

### 3. تشغيل التطبيق
```bash
python main.py
```

## هيكل المشروع

```
مختبر الصحة العامة المركزي/
├── main.py                 # الملف الرئيسي للتطبيق
├── database.py             # إدارة قاعدة البيانات
├── ui_components.py        # مكونات واجهة المستخدم
├── data_entry.py          # تبويب إدخال البيانات
├── work_tab.py            # تبويب العمل
├── results_tab.py         # تبويب النتائج
├── reports_tab.py         # تبويب التقارير
├── settings_tab.py        # تبويب الإعدادات
├── printer_manager.py     # إدارة الطباعة
├── requirements.txt       # المكتبات المطلوبة
├── README.md             # ملف التعليمات
└── lab_database.db       # قاعدة البيانات (يتم إنشاؤها تلقائياً)
```

## قاعدة البيانات

يستخدم التطبيق قاعدة بيانات SQLite مع الجداول التالية:
- `patients` - بيانات المرضى والعينات
- `tests` - التحاليل المتاحة
- `patient_tests` - ربط المرضى بالتحاليل والنتائج
- `batches` - الوجبات
- `sample_types` - أنواع العينات
- `sender_organizations` - جهات الإرسال
- `technicians` - الفنيين
- `report_settings` - إعدادات التقارير
- `counters` - العدادات التلقائية

## الاستخدام

### إدخال البيانات
1. انتقل إلى تبويب "إدخال البيانات"
2. املأ جميع الحقول المطلوبة (المميزة بـ *)
3. اختر التحاليل المطلوبة
4. اضغط "إضافة" لحفظ البيانات
5. سيتم طباعة الستيكر تلقائياً

### إدارة الوجبات
1. انتقل إلى تبويب "العمل"
2. حدد فترة العمل (من تاريخ إلى تاريخ)
3. اضغط "إنشاء الوجبة"
4. عين الفني المسؤول
5. اضغط "إرسال للنتائج" عند الانتهاء

### إدخال النتائج
1. انتقل إلى تبويب "النتائج"
2. أدخل رقم الوجبة واضغط "عرض"
3. اختر العينة من الجدول
4. اختر النتيجة واضغط "حفظ النتيجة"

### إنشاء التقارير
1. انتقل إلى تبويب "التقارير والإحصائيات"
2. استخدم الفلاتر لتحديد البيانات المطلوبة
3. اضغط "تطبيق الفلتر"
4. اختر نوع التقرير (مفرد أو جماعي)
5. اضغط "معاينة التقرير" ثم "طباعة"

## الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:
- تأكد من تثبيت جميع المكتبات المطلوبة
- تحقق من اتصال الطابعة
- راجع ملف قاعدة البيانات للتأكد من سلامة البيانات

## الترخيص

هذا التطبيق مطور خصيصاً لمختبر الصحة العامة المركزي في ذي قار.

## إصدارات التحديث

### الإصدار 1.0.0
- إطلاق التطبيق الأولي
- جميع المميزات الأساسية
- واجهة مستخدم عربية
- دعم الطباعة التلقائية
