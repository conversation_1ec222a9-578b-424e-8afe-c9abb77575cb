#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة إدارة قاعدة البيانات
Database Management Module
"""

import sqlite3
import datetime
from pathlib import Path
import os

class DatabaseManager:
    def __init__(self, db_path="lab_database.db"):
        self.db_path = db_path
        self.connection = None
        self.connect()
        self.create_tables()
        self.initialize_default_data()
        
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = sqlite3.connect(self.db_path, check_same_thread=False)
            self.connection.row_factory = sqlite3.Row
            return True
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
            
    def create_tables(self):
        """إنشاء الجداول"""
        cursor = self.connection.cursor()
        
        # جدول المرضى والعينات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS patients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                national_id INTEGER UNIQUE,
                name TEXT NOT NULL,
                age INTEGER NOT NULL,
                gender TEXT NOT NULL CHECK (gender IN ('M', 'F')),
                address TEXT NOT NULL,
                phone TEXT NOT NULL,
                passport_number TEXT,
                receipt_number TEXT,
                sample_type TEXT NOT NULL,
                sender_organization TEXT NOT NULL,
                sample_collection_date DATE NOT NULL,
                sample_received_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول التحاليل
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS tests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول تحاليل المرضى
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS patient_tests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                patient_id INTEGER NOT NULL,
                test_id INTEGER NOT NULL,
                result TEXT CHECK (result IN ('Negative', 'Positive', 'Retest', 'Recollection', 'Sent', 'TND')),
                batch_id INTEGER,
                technician_name TEXT,
                work_date DATE,
                result_date DATE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (patient_id) REFERENCES patients (id),
                FOREIGN KEY (test_id) REFERENCES tests (id),
                FOREIGN KEY (batch_id) REFERENCES batches (id)
            )
        ''')
        
        # جدول الوجبات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS batches (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                batch_number INTEGER UNIQUE,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                work_date DATE,
                status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'sent')),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول أنواع العينات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sample_types (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول جهات الإرسال
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sender_organizations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                address TEXT,
                phone TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الفنيين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS technicians (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                specialization TEXT,
                phone TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول إعدادات التقارير
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS report_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_name TEXT NOT NULL UNIQUE,
                setting_value TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول العدادات التلقائية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS counters (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                counter_name TEXT NOT NULL UNIQUE,
                counter_value INTEGER DEFAULT 0,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        self.connection.commit()
        
    def initialize_default_data(self):
        """إدراج البيانات الافتراضية"""
        cursor = self.connection.cursor()
        
        # إدراج العدادات الافتراضية
        counters = [
            ('national_id', 0),
            ('batch_number', 0)
        ]
        
        for counter_name, counter_value in counters:
            cursor.execute('''
                INSERT OR IGNORE INTO counters (counter_name, counter_value)
                VALUES (?, ?)
            ''', (counter_name, counter_value))
            
        # إدراج أنواع العينات الافتراضية
        sample_types = [
            'دم',
            'بول',
            'براز',
            'لعاب',
            'مسحة أنف',
            'مسحة حلق'
        ]
        
        for sample_type in sample_types:
            cursor.execute('''
                INSERT OR IGNORE INTO sample_types (name)
                VALUES (?)
            ''', (sample_type,))
            
        # إدراج التحاليل الافتراضية
        tests = [
            'فحص كوفيد-19',
            'فحص الدم الشامل',
            'فحص السكر',
            'فحص الكوليسترول',
            'فحص وظائف الكبد',
            'فحص وظائف الكلى'
        ]
        
        for test in tests:
            cursor.execute('''
                INSERT OR IGNORE INTO tests (name)
                VALUES (?)
            ''', (test,))
            
        # إدراج جهات الإرسال الافتراضية
        organizations = [
            'مستشفى الناصرية العام',
            'مستشفى الحبوبي',
            'مركز صحي الشطرة',
            'مركز صحي الرفاعي',
            'مركز صحي سوق الشيوخ'
        ]
        
        for org in organizations:
            cursor.execute('''
                INSERT OR IGNORE INTO sender_organizations (name)
                VALUES (?)
            ''', (org,))
            
        # إدراج إعدادات التقارير الافتراضية
        report_settings = [
            ('header_right', 'وزارة الصحة العراقية'),
            ('header_left', 'مختبر الصحة العامة المركزي'),
            ('footer_address', 'الناصرية - ذي قار - العراق'),
            ('footer_email', '<EMAIL>'),
            ('logo_path', '')
        ]
        
        for setting_name, setting_value in report_settings:
            cursor.execute('''
                INSERT OR IGNORE INTO report_settings (setting_name, setting_value)
                VALUES (?, ?)
            ''', (setting_name, setting_value))
            
        self.connection.commit()
        
    def get_next_counter(self, counter_name):
        """الحصول على الرقم التالي للعداد"""
        cursor = self.connection.cursor()
        cursor.execute('''
            UPDATE counters 
            SET counter_value = counter_value + 1,
                updated_at = CURRENT_TIMESTAMP
            WHERE counter_name = ?
        ''', (counter_name,))
        
        cursor.execute('''
            SELECT counter_value FROM counters WHERE counter_name = ?
        ''', (counter_name,))
        
        result = cursor.fetchone()
        self.connection.commit()
        return result['counter_value'] if result else 1
        
    def execute_query(self, query, params=None):
        """تنفيذ استعلام"""
        try:
            cursor = self.connection.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            self.connection.commit()
            return cursor
        except Exception as e:
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            return None
            
    def fetch_all(self, query, params=None):
        """جلب جميع النتائج"""
        cursor = self.execute_query(query, params)
        if cursor:
            return cursor.fetchall()
        return []
        
    def fetch_one(self, query, params=None):
        """جلب نتيجة واحدة"""
        cursor = self.execute_query(query, params)
        if cursor:
            return cursor.fetchone()
        return None
        
    def close(self):
        """إغلاق الاتصال"""
        if self.connection:
            self.connection.close()
