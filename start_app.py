#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل التطبيق مع معالجة الأخطاء
Application Launcher with Error Handling
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

def check_requirements():
    """التحقق من المتطلبات"""
    missing_modules = []
    
    try:
        import sqlite3
    except ImportError:
        missing_modules.append("sqlite3")
        
    try:
        import datetime
    except ImportError:
        missing_modules.append("datetime")
        
    try:
        import pathlib
    except ImportError:
        missing_modules.append("pathlib")
        
    # التحقق من المكتبات الاختيارية
    optional_modules = {
        'pandas': 'لاستيراد ملفات Excel',
        'PIL': 'لمعالجة الصور والطباعة',
        'win32print': 'للطباعة على Windows'
    }
    
    missing_optional = []
    for module, description in optional_modules.items():
        try:
            __import__(module)
        except ImportError:
            missing_optional.append(f"{module} - {description}")
    
    return missing_modules, missing_optional

def show_requirements_dialog(missing_modules, missing_optional):
    """عرض نافذة المتطلبات المفقودة"""
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    message = "تم اكتشاف مكتبات مفقودة:\n\n"
    
    if missing_modules:
        message += "مكتبات أساسية مفقودة:\n"
        for module in missing_modules:
            message += f"- {module}\n"
        message += "\n"
    
    if missing_optional:
        message += "مكتبات اختيارية مفقودة (قد تؤثر على بعض الوظائف):\n"
        for module in missing_optional:
            message += f"- {module}\n"
        message += "\n"
    
    message += "هل تريد المتابعة على أي حال؟"
    
    result = messagebox.askyesno("مكتبات مفقودة", message)
    root.destroy()
    
    return result

def main():
    """الدالة الرئيسية"""
    print("========================================")
    print("  مختبر الصحة العامة المركزي - ذي قار")
    print("  Central Public Health Laboratory")
    print("========================================")
    print()
    
    # التحقق من المتطلبات
    print("التحقق من المتطلبات...")
    missing_modules, missing_optional = check_requirements()
    
    if missing_modules:
        print("خطأ: مكتبات أساسية مفقودة!")
        for module in missing_modules:
            print(f"- {module}")
        print("\nيرجى تثبيت المكتبات المطلوبة أولاً")
        input("اضغط Enter للخروج...")
        return
    
    if missing_optional:
        print("تحذير: بعض المكتبات الاختيارية مفقودة:")
        for module in missing_optional:
            print(f"- {module}")
        print()
        
        # عرض نافذة الاختيار
        if not show_requirements_dialog([], missing_optional):
            print("تم إلغاء تشغيل التطبيق")
            return
    
    print("تشغيل التطبيق...")
    print()
    
    try:
        # استيراد وتشغيل التطبيق
        from main import LabManagementSystem
        
        app = LabManagementSystem()
        app.run()
        
    except ImportError as e:
        print(f"خطأ في استيراد التطبيق: {e}")
        print("تأكد من وجود جميع ملفات التطبيق في نفس المجلد")
        input("اضغط Enter للخروج...")
        
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        
        # عرض نافذة الخطأ
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("خطأ في التطبيق", f"حدث خطأ أثناء تشغيل التطبيق:\n\n{str(e)}")
        root.destroy()
        
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
