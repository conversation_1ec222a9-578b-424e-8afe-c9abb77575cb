========================================
  مختبر الصحة العامة المركزي - ذي قار
  دليل التشغيل السريع
========================================

🚀 طرق تشغيل التطبيق:

1️⃣ الطريقة الأسهل:
   انقر مزدوج على: تشغيل_التطبيق.bat

2️⃣ طرق أخرى:
   - RUN_DEMO.bat (للنسخة التجريبية)
   - START.bat (للتطبيق الكامل)
   - python DIRECT_RUN.py

========================================

📋 متطلبات التشغيل:

✅ Python 3.8 أو أحدث
   - حمل من: https://www.python.org/downloads/
   - تأكد من اختيار "Add Python to PATH"

✅ نظام Windows
✅ جميع ملفات التطبيق في نفس المجلد

========================================

🔧 حل المشاكل:

❌ "Python was not found"
   الحل: تثبيت Python من الموقع الرسمي

❌ "No module named..."
   الحل: تشغيل install.bat

❌ التطبيق لا يفتح
   الحل: تشغيل python DIRECT_RUN.py من Command Prompt

========================================

📞 المساعدة:

📁 الملفات المهمة:
- DIRECT_RUN.py ← تشغيل مباشر
- demo_app.py ← نسخة تجريبية مبسطة
- main.py ← التطبيق الكامل
- README.md ← دليل شامل

🎯 النسخ المتوفرة:
- نسخة تجريبية: وظائف أساسية
- نسخة كاملة: جميع المميزات

========================================

✨ مميزات التطبيق:

📋 إدخال بيانات المرضى
🔬 إدارة العينات والوجبات
📊 النتائج والتحاليل
📈 التقارير والإحصائيات
⚙️ الإعدادات والتخصيص
🖨️ طباعة الستيكر والتقارير

========================================

تم التطوير بواسطة: Augment Agent
التاريخ: 2024
الإصدار: 1.0.0

للدعم الفني: راجع ملف README.md
