#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخة تجريبية من تطبيق مختبر الصحة العامة المركزي
Demo Version of Central Public Health Laboratory Application
"""

import tkinter as tk
from tkinter import ttk, messagebox
import datetime
import sqlite3
import os

class LabDemoApp:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.create_database()
        self.create_ui()
        
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("مختبر الصحة العامة المركزي - ذي قار (نسخة تجريبية)")
        self.root.geometry("1200x700")
        self.root.configure(bg='#F8F9FA')
        
        # الألوان
        self.colors = {
            'primary': '#2E86AB',
            'success': '#28A745',
            'warning': '#FFC107',
            'danger': '#DC3545',
            'background': '#F8F9FA',
            'surface': '#FFFFFF'
        }
        
    def create_database(self):
        """إنشاء قاعدة بيانات مبسطة"""
        self.conn = sqlite3.connect('demo_lab.db')
        cursor = self.conn.cursor()
        
        # جدول المرضى
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS patients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                national_id INTEGER UNIQUE,
                name TEXT NOT NULL,
                age INTEGER,
                gender TEXT,
                sample_type TEXT,
                result TEXT,
                date_added DATE DEFAULT CURRENT_DATE
            )
        ''')
        
        self.conn.commit()
        
    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg=self.colors['background'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # العنوان
        title_frame = tk.Frame(main_frame, bg=self.colors['primary'], height=80)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(
            title_frame,
            text="مختبر الصحة العامة المركزي - ذي قار",
            font=('Arial', 18, 'bold'),
            fg='white',
            bg=self.colors['primary']
        )
        title_label.pack(expand=True)
        
        # التبويبات
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # تبويب إدخال البيانات
        self.create_data_entry_tab()
        
        # تبويب عرض البيانات
        self.create_view_data_tab()
        
        # تبويب الإحصائيات
        self.create_stats_tab()
        
    def create_data_entry_tab(self):
        """تبويب إدخال البيانات"""
        tab_frame = tk.Frame(self.notebook, bg=self.colors['surface'])
        self.notebook.add(tab_frame, text="إدخال البيانات")
        
        # نموذج الإدخال
        form_frame = tk.LabelFrame(
            tab_frame,
            text="بيانات المريض",
            font=('Arial', 12, 'bold'),
            bg=self.colors['surface'],
            fg=self.colors['primary']
        )
        form_frame.pack(fill=tk.X, padx=20, pady=20)
        
        # الحقول
        self.entries = {}
        
        fields = [
            ("name", "الاسم"),
            ("age", "العمر"),
            ("gender", "الجنس (M/F)"),
            ("sample_type", "نوع العينة")
        ]
        
        for i, (field, label) in enumerate(fields):
            tk.Label(
                form_frame,
                text=f"{label}:",
                font=('Arial', 10, 'bold'),
                bg=self.colors['surface']
            ).grid(row=i, column=0, sticky="e", padx=10, pady=5)
            
            entry = tk.Entry(form_frame, font=('Arial', 10), width=30)
            entry.grid(row=i, column=1, sticky="w", padx=10, pady=5)
            self.entries[field] = entry
        
        # الرقم الوطني (تلقائي)
        tk.Label(
            form_frame,
            text="الرقم الوطني:",
            font=('Arial', 10, 'bold'),
            bg=self.colors['surface']
        ).grid(row=len(fields), column=0, sticky="e", padx=10, pady=5)
        
        self.national_id_var = tk.StringVar()
        national_id_entry = tk.Entry(
            form_frame,
            textvariable=self.national_id_var,
            font=('Arial', 10),
            width=30,
            state='readonly'
        )
        national_id_entry.grid(row=len(fields), column=1, sticky="w", padx=10, pady=5)
        
        # أزرار العمليات
        buttons_frame = tk.Frame(form_frame, bg=self.colors['surface'])
        buttons_frame.grid(row=len(fields)+1, column=0, columnspan=2, pady=20)
        
        tk.Button(
            buttons_frame,
            text="إضافة مريض",
            command=self.add_patient,
            bg=self.colors['success'],
            fg='white',
            font=('Arial', 10, 'bold'),
            width=15,
            height=2
        ).pack(side=tk.LEFT, padx=10)
        
        tk.Button(
            buttons_frame,
            text="مسح النموذج",
            command=self.clear_form,
            bg=self.colors['warning'],
            fg='black',
            font=('Arial', 10, 'bold'),
            width=15,
            height=2
        ).pack(side=tk.LEFT, padx=10)
        
        # تحديث الرقم الوطني
        self.update_national_id()
        
    def create_view_data_tab(self):
        """تبويب عرض البيانات"""
        tab_frame = tk.Frame(self.notebook, bg=self.colors['surface'])
        self.notebook.add(tab_frame, text="عرض البيانات")
        
        # جدول البيانات
        columns = ("الرقم الوطني", "الاسم", "العمر", "الجنس", "نوع العينة", "النتيجة", "التاريخ")
        
        self.tree = ttk.Treeview(tab_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120, anchor='center')
        
        # شريط التمرير
        scrollbar = tk.Scrollbar(tab_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=20, pady=20)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=20)
        
        # أزرار العمليات
        operations_frame = tk.Frame(tab_frame, bg=self.colors['surface'])
        operations_frame.pack(fill=tk.X, padx=20, pady=10)
        
        tk.Button(
            operations_frame,
            text="تحديث البيانات",
            command=self.refresh_data,
            bg=self.colors['primary'],
            fg='white',
            font=('Arial', 10, 'bold'),
            width=15,
            height=2
        ).pack(side=tk.LEFT, padx=10)
        
        tk.Button(
            operations_frame,
            text="إضافة نتيجة",
            command=self.add_result,
            bg=self.colors['success'],
            fg='white',
            font=('Arial', 10, 'bold'),
            width=15,
            height=2
        ).pack(side=tk.LEFT, padx=10)
        
        tk.Button(
            operations_frame,
            text="حذف المحدد",
            command=self.delete_selected,
            bg=self.colors['danger'],
            fg='white',
            font=('Arial', 10, 'bold'),
            width=15,
            height=2
        ).pack(side=tk.LEFT, padx=10)
        
        # تحميل البيانات
        self.refresh_data()
        
    def create_stats_tab(self):
        """تبويب الإحصائيات"""
        tab_frame = tk.Frame(self.notebook, bg=self.colors['surface'])
        self.notebook.add(tab_frame, text="الإحصائيات")
        
        stats_frame = tk.LabelFrame(
            tab_frame,
            text="إحصائيات المختبر",
            font=('Arial', 12, 'bold'),
            bg=self.colors['surface'],
            fg=self.colors['primary']
        )
        stats_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # الإحصائيات
        self.stats_labels = {}
        
        stats = [
            ("total", "إجمالي المرضى"),
            ("today", "مرضى اليوم"),
            ("positive", "نتائج إيجابية"),
            ("negative", "نتائج سلبية")
        ]
        
        for i, (key, label) in enumerate(stats):
            row = i // 2
            col = (i % 2) * 2
            
            tk.Label(
                stats_frame,
                text=f"{label}:",
                font=('Arial', 14, 'bold'),
                bg=self.colors['surface']
            ).grid(row=row, column=col, sticky="e", padx=20, pady=20)
            
            value_label = tk.Label(
                stats_frame,
                text="0",
                font=('Arial', 16, 'bold'),
                fg=self.colors['primary'],
                bg=self.colors['surface']
            )
            value_label.grid(row=row, column=col+1, sticky="w", padx=20, pady=20)
            
            self.stats_labels[key] = value_label
        
        # زر تحديث الإحصائيات
        tk.Button(
            stats_frame,
            text="تحديث الإحصائيات",
            command=self.update_stats,
            bg=self.colors['primary'],
            fg='white',
            font=('Arial', 12, 'bold'),
            width=20,
            height=2
        ).grid(row=2, column=0, columnspan=4, pady=30)
        
        # تحديث الإحصائيات
        self.update_stats()
        
    def get_next_national_id(self):
        """الحصول على الرقم الوطني التالي"""
        cursor = self.conn.cursor()
        cursor.execute("SELECT MAX(national_id) FROM patients")
        result = cursor.fetchone()
        return (result[0] or 0) + 1
        
    def update_national_id(self):
        """تحديث الرقم الوطني"""
        next_id = self.get_next_national_id()
        self.national_id_var.set(str(next_id))
        
    def add_patient(self):
        """إضافة مريض جديد"""
        # التحقق من البيانات
        name = self.entries['name'].get().strip()
        if not name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المريض")
            return
            
        try:
            age = int(self.entries['age'].get())
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال عمر صحيح")
            return
            
        gender = self.entries['gender'].get().strip().upper()
        if gender not in ['M', 'F']:
            messagebox.showerror("خطأ", "يرجى إدخال الجنس (M أو F)")
            return
            
        sample_type = self.entries['sample_type'].get().strip()
        if not sample_type:
            messagebox.showerror("خطأ", "يرجى إدخال نوع العينة")
            return
            
        # إدراج البيانات
        try:
            national_id = self.get_next_national_id()
            cursor = self.conn.cursor()
            cursor.execute('''
                INSERT INTO patients (national_id, name, age, gender, sample_type)
                VALUES (?, ?, ?, ?, ?)
            ''', (national_id, name, age, gender, sample_type))
            
            self.conn.commit()
            
            messagebox.showinfo("نجح", f"تم إضافة المريض بنجاح\nالرقم الوطني: {national_id}")
            self.clear_form()
            self.refresh_data()
            self.update_stats()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إضافة المريض:\n{str(e)}")
            
    def clear_form(self):
        """مسح النموذج"""
        for entry in self.entries.values():
            entry.delete(0, tk.END)
        self.update_national_id()
        
    def refresh_data(self):
        """تحديث البيانات"""
        # مسح الجدول
        for item in self.tree.get_children():
            self.tree.delete(item)
            
        # جلب البيانات
        cursor = self.conn.cursor()
        cursor.execute("SELECT * FROM patients ORDER BY national_id DESC")
        patients = cursor.fetchall()
        
        # إدراج البيانات
        for patient in patients:
            gender_text = 'ذكر' if patient[4] == 'M' else 'أنثى'
            self.tree.insert('', tk.END, values=(
                patient[1],  # national_id
                patient[2],  # name
                patient[3],  # age
                gender_text,  # gender
                patient[5],  # sample_type
                patient[6] or 'لم تحدد',  # result
                patient[7]   # date_added
            ), tags=(patient[0],))  # id
            
    def add_result(self):
        """إضافة نتيجة"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مريض لإضافة النتيجة")
            return
            
        # نافذة إدخال النتيجة
        result_window = tk.Toplevel(self.root)
        result_window.title("إضافة نتيجة")
        result_window.geometry("300x200")
        result_window.configure(bg=self.colors['surface'])
        
        tk.Label(
            result_window,
            text="اختر النتيجة:",
            font=('Arial', 12, 'bold'),
            bg=self.colors['surface']
        ).pack(pady=20)
        
        result_var = tk.StringVar()
        results = ['Negative', 'Positive', 'Retest', 'Pending']
        
        for result in results:
            tk.Radiobutton(
                result_window,
                text=result,
                variable=result_var,
                value=result,
                font=('Arial', 10),
                bg=self.colors['surface']
            ).pack(pady=5)
            
        def save_result():
            if not result_var.get():
                messagebox.showwarning("تحذير", "يرجى اختيار النتيجة")
                return
                
            item = self.tree.item(selection[0])
            patient_id = item['tags'][0]
            
            cursor = self.conn.cursor()
            cursor.execute(
                "UPDATE patients SET result = ? WHERE id = ?",
                (result_var.get(), patient_id)
            )
            self.conn.commit()
            
            messagebox.showinfo("نجح", "تم حفظ النتيجة بنجاح")
            result_window.destroy()
            self.refresh_data()
            self.update_stats()
            
        tk.Button(
            result_window,
            text="حفظ النتيجة",
            command=save_result,
            bg=self.colors['success'],
            fg='white',
            font=('Arial', 10, 'bold')
        ).pack(pady=20)
        
    def delete_selected(self):
        """حذف المحدد"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مريض للحذف")
            return
            
        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا المريض؟"):
            item = self.tree.item(selection[0])
            patient_id = item['tags'][0]
            
            cursor = self.conn.cursor()
            cursor.execute("DELETE FROM patients WHERE id = ?", (patient_id,))
            self.conn.commit()
            
            messagebox.showinfo("نجح", "تم حذف المريض بنجاح")
            self.refresh_data()
            self.update_stats()
            
    def update_stats(self):
        """تحديث الإحصائيات"""
        cursor = self.conn.cursor()
        
        # إجمالي المرضى
        cursor.execute("SELECT COUNT(*) FROM patients")
        total = cursor.fetchone()[0]
        self.stats_labels['total'].configure(text=str(total))
        
        # مرضى اليوم
        today = datetime.date.today().strftime('%Y-%m-%d')
        cursor.execute("SELECT COUNT(*) FROM patients WHERE date_added = ?", (today,))
        today_count = cursor.fetchone()[0]
        self.stats_labels['today'].configure(text=str(today_count))
        
        # النتائج الإيجابية
        cursor.execute("SELECT COUNT(*) FROM patients WHERE result = 'Positive'")
        positive = cursor.fetchone()[0]
        self.stats_labels['positive'].configure(text=str(positive))
        
        # النتائج السلبية
        cursor.execute("SELECT COUNT(*) FROM patients WHERE result = 'Negative'")
        negative = cursor.fetchone()[0]
        self.stats_labels['negative'].configure(text=str(negative))
        
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()
        
    def __del__(self):
        """إغلاق قاعدة البيانات"""
        if hasattr(self, 'conn'):
            self.conn.close()

if __name__ == "__main__":
    print("========================================")
    print("  مختبر الصحة العامة المركزي - ذي قار")
    print("  نسخة تجريبية")
    print("========================================")
    print()
    
    try:
        app = LabDemoApp()
        app.run()
    except Exception as e:
        print(f"خطأ: {e}")
        input("اضغط Enter للخروج...")
