#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف التكوين للتطبيق
Application Configuration File
"""

import os
from pathlib import Path

# مسارات التطبيق
APP_DIR = Path(__file__).parent
DATA_DIR = APP_DIR / "data"
ASSETS_DIR = APP_DIR / "assets"
REPORTS_DIR = APP_DIR / "reports"
BACKUPS_DIR = APP_DIR / "backups"

# إنشاء المجلدات إذا لم تكن موجودة
for directory in [DATA_DIR, ASSETS_DIR, REPORTS_DIR, BACKUPS_DIR]:
    directory.mkdir(exist_ok=True)

# إعدادات قاعدة البيانات
DATABASE_CONFIG = {
    'path': DATA_DIR / "lab_database.db",
    'backup_interval': 24,  # ساعات
    'max_backups': 30,  # عدد النسخ الاحتياطية المحفوظة
}

# إعدادات واجهة المستخدم
UI_CONFIG = {
    'window_title': "مختبر الصحة العامة المركزي - ذي قار",
    'window_size': "1400x800",
    'min_window_size': "1200x700",
    'theme': {
        'primary': '#2E86AB',
        'secondary': '#A23B72',
        'accent': '#F18F01',
        'background': '#F8F9FA',
        'surface': '#FFFFFF',
        'text': '#212529',
        'text_secondary': '#6C757D'
    },
    'fonts': {
        'default': ('Arial', 10, 'bold'),
        'title': ('Arial', 16, 'bold'),
        'subtitle': ('Arial', 12, 'bold'),
        'small': ('Arial', 8, 'bold')
    }
}

# إعدادات الطباعة
PRINTER_CONFIG = {
    'auto_detect': True,
    'sticker_size': (400, 200),  # بكسل
    'report_margins': (50, 50, 50, 50),  # يسار، أعلى، يمين، أسفل
    'default_font_size': 12,
}

# إعدادات التقارير
REPORT_CONFIG = {
    'logo_path': ASSETS_DIR / "logo.png",
    'header_height': 100,
    'footer_height': 50,
    'default_settings': {
        'header_right': 'وزارة الصحة العراقية',
        'header_left': 'مختبر الصحة العامة المركزي',
        'footer_address': 'الناصرية - ذي قار - العراق',
        'footer_email': '<EMAIL>',
    }
}

# إعدادات الاستيراد والتصدير
IMPORT_EXPORT_CONFIG = {
    'excel_formats': ['.xlsx', '.xls'],
    'max_file_size': 50 * 1024 * 1024,  # 50 MB
    'encoding': 'utf-8',
    'date_format': '%Y-%m-%d',
    'datetime_format': '%Y-%m-%d %H:%M:%S',
}

# إعدادات الأمان
SECURITY_CONFIG = {
    'backup_on_startup': True,
    'auto_save_interval': 300,  # ثواني
    'session_timeout': 3600,  # ثواني
    'max_login_attempts': 3,
}

# إعدادات الأداء
PERFORMANCE_CONFIG = {
    'max_records_per_page': 1000,
    'cache_size': 100,
    'connection_timeout': 30,
    'query_timeout': 60,
}

# إعدادات التسجيل
LOGGING_CONFIG = {
    'level': 'INFO',
    'file_path': DATA_DIR / "app.log",
    'max_file_size': 10 * 1024 * 1024,  # 10 MB
    'backup_count': 5,
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
}

# إعدادات التحديث
UPDATE_CONFIG = {
    'check_for_updates': True,
    'update_url': 'https://api.github.com/repos/lab-management/central-health-lab/releases/latest',
    'auto_update': False,
}

# إعدادات اللغة
LANGUAGE_CONFIG = {
    'default_language': 'ar',
    'supported_languages': ['ar', 'en'],
    'rtl_support': True,
}

# إعدادات التطبيق العامة
APP_CONFIG = {
    'version': '1.0.0',
    'build': '20240101',
    'debug_mode': False,
    'developer_mode': False,
    'first_run': True,
}

def get_config(section=None):
    """الحصول على إعدادات معينة"""
    configs = {
        'database': DATABASE_CONFIG,
        'ui': UI_CONFIG,
        'printer': PRINTER_CONFIG,
        'report': REPORT_CONFIG,
        'import_export': IMPORT_EXPORT_CONFIG,
        'security': SECURITY_CONFIG,
        'performance': PERFORMANCE_CONFIG,
        'logging': LOGGING_CONFIG,
        'update': UPDATE_CONFIG,
        'language': LANGUAGE_CONFIG,
        'app': APP_CONFIG,
    }
    
    if section:
        return configs.get(section, {})
    return configs

def update_config(section, key, value):
    """تحديث إعداد معين"""
    configs = get_config()
    if section in configs and key in configs[section]:
        configs[section][key] = value
        return True
    return False

def save_config():
    """حفظ الإعدادات (يمكن تطويرها لاحقاً لحفظ في ملف)"""
    # يمكن إضافة حفظ الإعدادات في ملف JSON أو INI
    pass

def load_config():
    """تحميل الإعدادات (يمكن تطويرها لاحقاً للتحميل من ملف)"""
    # يمكن إضافة تحميل الإعدادات من ملف JSON أو INI
    pass

# تحميل الإعدادات عند استيراد الملف
load_config()
