#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة إدارة الطابعة
Printer Management Module
"""

import win32print
import win32ui
import win32con
from PIL import Image, ImageDraw, ImageFont
import tempfile
import os
from tkinter import messagebox

class PrinterManager:
    def __init__(self):
        self.default_printer = None
        self.detect_printers()
        
    def detect_printers(self):
        """اكتشاف الطابعات المتاحة"""
        try:
            # الحصول على قائمة الطابعات
            printers = win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL | win32print.PRINTER_ENUM_CONNECTIONS)
            
            if printers:
                # استخدام الطابعة الافتراضية
                self.default_printer = win32print.GetDefaultPrinter()
                print(f"الطابعة الافتراضية: {self.default_printer}")
            else:
                print("لم يتم العثور على طابعات")
                
        except Exception as e:
            print(f"خطأ في اكتشاف الطابعات: {e}")
            
    def get_available_printers(self):
        """الحصول على قائمة الطابعات المتاحة"""
        try:
            printers = win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL | win32print.PRINTER_ENUM_CONNECTIONS)
            return [printer[2] for printer in printers]
        except Exception as e:
            print(f"خطأ في الحصول على قائمة الطابعات: {e}")
            return []
            
    def set_default_printer(self, printer_name):
        """تعيين الطابعة الافتراضية"""
        try:
            win32print.SetDefaultPrinter(printer_name)
            self.default_printer = printer_name
            return True
        except Exception as e:
            print(f"خطأ في تعيين الطابعة الافتراضية: {e}")
            return False
            
    def create_sticker_image(self, sticker_data):
        """إنشاء صورة الستيكر"""
        try:
            # أبعاد الستيكر (بالبكسل)
            width, height = 400, 200
            
            # إنشاء صورة جديدة
            image = Image.new('RGB', (width, height), 'white')
            draw = ImageDraw.Draw(image)
            
            # تحديد الخطوط
            try:
                # محاولة استخدام خط عربي
                title_font = ImageFont.truetype("arial.ttf", 16)
                text_font = ImageFont.truetype("arial.ttf", 12)
            except:
                # استخدام الخط الافتراضي
                title_font = ImageFont.load_default()
                text_font = ImageFont.load_default()
                
            # رسم الحدود
            draw.rectangle([5, 5, width-5, height-5], outline='black', width=2)
            
            # عنوان الستيكر
            title = "مختبر الصحة العامة المركزي - ذي قار"
            title_bbox = draw.textbbox((0, 0), title, font=title_font)
            title_width = title_bbox[2] - title_bbox[0]
            draw.text(((width - title_width) // 2, 15), title, fill='black', font=title_font)
            
            # خط فاصل
            draw.line([20, 40, width-20, 40], fill='black', width=1)
            
            # بيانات المريض
            y_pos = 55
            line_height = 25
            
            # اسم المريض
            patient_name = f"الاسم: {sticker_data['name']}"
            draw.text((20, y_pos), patient_name, fill='black', font=text_font)
            y_pos += line_height
            
            # الرقم الوطني
            national_id = f"الرقم الوطني: {sticker_data['national_id']}"
            draw.text((20, y_pos), national_id, fill='black', font=text_font)
            y_pos += line_height
            
            # نوع العينة
            sample_type = f"نوع العينة: {sticker_data['sample_type']}"
            draw.text((20, y_pos), sample_type, fill='black', font=text_font)
            y_pos += line_height
            
            # التاريخ
            date = f"التاريخ: {sticker_data['date']}"
            draw.text((20, y_pos), date, fill='black', font=text_font)
            
            return image
            
        except Exception as e:
            print(f"خطأ في إنشاء صورة الستيكر: {e}")
            return None
            
    def print_sticker(self, sticker_data):
        """طباعة الستيكر"""
        if not self.default_printer:
            messagebox.showerror("خطأ", "لم يتم العثور على طابعة")
            return False
            
        try:
            # إنشاء صورة الستيكر
            image = self.create_sticker_image(sticker_data)
            if not image:
                return False
                
            # حفظ الصورة مؤقتاً
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.bmp')
            image.save(temp_file.name, 'BMP')
            temp_file.close()
            
            # طباعة الصورة
            self.print_image(temp_file.name)
            
            # حذف الملف المؤقت
            os.unlink(temp_file.name)
            
            return True
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء طباعة الستيكر: {str(e)}")
            return False
            
    def print_image(self, image_path):
        """طباعة صورة"""
        try:
            # فتح الطابعة
            hprinter = win32print.OpenPrinter(self.default_printer)
            
            try:
                # بدء مهمة الطباعة
                hdc = win32ui.CreateDC()
                hdc.CreatePrinterDC(self.default_printer)
                
                # بدء الصفحة
                hdc.StartDoc("Lab Sticker")
                hdc.StartPage()
                
                # تحميل الصورة
                bmp = Image.open(image_path)
                
                # الحصول على أبعاد الطابعة
                printer_width = hdc.GetDeviceCaps(win32con.HORZRES)
                printer_height = hdc.GetDeviceCaps(win32con.VERTRES)
                
                # حساب أبعاد الطباعة (تصغير الصورة لتناسب الستيكر)
                sticker_width = min(printer_width // 2, 300)  # نصف عرض الصفحة أو 300 بكسل
                sticker_height = int(sticker_width * bmp.height / bmp.width)
                
                # تحويل الصورة إلى تنسيق مناسب للطباعة
                dib = win32ui.CreateBitmap()
                dib.LoadBitmapFile(image_path)
                
                # طباعة الصورة
                hdc.StretchBlt(
                    (0, 0),  # موضع البداية
                    (sticker_width, sticker_height),  # الأبعاد
                    dib.GetHandle(),
                    (0, 0),  # موضع البداية في الصورة
                    (bmp.width, bmp.height),  # أبعاد الصورة الأصلية
                    win32con.SRCCOPY
                )
                
                # إنهاء الطباعة
                hdc.EndPage()
                hdc.EndDoc()
                
            finally:
                win32print.ClosePrinter(hprinter)
                
        except Exception as e:
            print(f"خطأ في طباعة الصورة: {e}")
            raise
            
    def print_report(self, report_content, title="تقرير المختبر"):
        """طباعة تقرير"""
        if not self.default_printer:
            messagebox.showerror("خطأ", "لم يتم العثور على طابعة")
            return False
            
        try:
            # فتح الطابعة
            hprinter = win32print.OpenPrinter(self.default_printer)
            
            try:
                # بدء مهمة الطباعة
                hdc = win32ui.CreateDC()
                hdc.CreatePrinterDC(self.default_printer)
                
                # بدء الصفحة
                hdc.StartDoc(title)
                hdc.StartPage()
                
                # إعداد الخط
                font_dict = {
                    'name': 'Arial',
                    'height': 12,
                    'weight': win32con.FW_NORMAL,
                }
                font = win32ui.CreateFont(font_dict)
                hdc.SelectObject(font)
                
                # طباعة المحتوى
                lines = report_content.split('\n')
                y_pos = 100
                line_height = 20
                
                for line in lines:
                    hdc.TextOut(100, y_pos, line)
                    y_pos += line_height
                    
                    # التحقق من نهاية الصفحة
                    if y_pos > hdc.GetDeviceCaps(win32con.VERTRES) - 200:
                        hdc.EndPage()
                        hdc.StartPage()
                        y_pos = 100
                
                # إنهاء الطباعة
                hdc.EndPage()
                hdc.EndDoc()
                
                return True
                
            finally:
                win32print.ClosePrinter(hprinter)
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء طباعة التقرير: {str(e)}")
            return False
            
    def test_printer(self):
        """اختبار الطابعة"""
        if not self.default_printer:
            return False
            
        try:
            # إنشاء ستيكر تجريبي
            test_data = {
                'name': 'اختبار الطباعة',
                'national_id': '12345',
                'sample_type': 'عينة تجريبية',
                'date': '2024-01-01'
            }
            
            return self.print_sticker(test_data)
            
        except Exception as e:
            print(f"خطأ في اختبار الطابعة: {e}")
            return False
