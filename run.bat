@echo off
echo ========================================
echo   مختبر الصحة العامة المركزي - ذي قار
echo   Central Public Health Laboratory
echo ========================================
echo.

echo التحقق من وجود Python...
echo Checking for Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo خطأ: Python غير مثبت على النظام
    echo Error: Python is not installed on the system
    echo.
    echo يرجى تشغيل install.bat أولاً لتثبيت المتطلبات
    echo Please run install.bat first to install requirements
    echo.
    pause
    exit /b 1
)

echo تشغيل التطبيق...
echo Starting application...
echo.

python main.py

if %errorlevel% neq 0 (
    echo.
    echo حدث خطأ أثناء تشغيل التطبيق
    echo Error occurred while running the application
    echo.
    echo إذا كانت هذه المرة الأولى، يرجى تشغيل install.bat أولاً
    echo If this is the first time, please run install.bat first
    echo.
)

echo.
echo تم إغلاق التطبيق
echo Application closed
pause
