@echo off
echo ========================================
echo   مختبر الصحة العامة المركزي - ذي قار
echo   Central Public Health Laboratory
echo   تثبيت المتطلبات - Installing Requirements
echo ========================================
echo.

echo التحقق من وجود Python...
echo Checking for Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo خطأ: Python غير مثبت على النظام
    echo Error: Python is not installed on the system
    echo.
    echo يرجى تحميل وتثبيت Python من:
    echo Please download and install Python from:
    echo https://www.python.org/downloads/
    echo.
    echo تأكد من اختيار "Add Python to PATH" أثناء التثبيت
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

echo تم العثور على Python
echo Python found
echo.

echo تثبيت المكتبات المطلوبة...
echo Installing required libraries...
echo.

pip install pandas openpyxl Pillow pywin32 python-dateutil xlrd

if %errorlevel% neq 0 (
    echo.
    echo حدث خطأ أثناء تثبيت المكتبات
    echo Error occurred during library installation
    echo.
    pause
    exit /b 1
)

echo.
echo تم تثبيت جميع المتطلبات بنجاح!
echo All requirements installed successfully!
echo.
echo يمكنك الآن تشغيل التطبيق باستخدام run.bat
echo You can now run the application using run.bat
echo.
pause
