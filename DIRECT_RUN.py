#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل مباشر للتطبيق
Direct Application Runner
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def main():
    """تشغيل التطبيق مباشرة"""
    
    # إنشاء نافذة بسيطة للتأكد من عمل Python
    root = tk.Tk()
    root.title("مختبر الصحة العامة المركزي - ذي قار")
    root.geometry("600x400")
    root.configure(bg='#F8F9FA')
    
    # توسيط النافذة
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (300)
    y = (root.winfo_screenheight() // 2) - (200)
    root.geometry(f"600x400+{x}+{y}")
    
    # الإطار الرئيسي
    main_frame = tk.Frame(root, bg='#FFFFFF', relief=tk.RAISED, bd=2)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    
    # العنوان
    title_label = tk.Label(
        main_frame,
        text="🎉 تم تشغيل التطبيق بنجاح! 🎉",
        font=('Arial', 18, 'bold'),
        fg='#28A745',
        bg='#FFFFFF'
    )
    title_label.pack(pady=30)
    
    # معلومات النظام
    info_text = f"""
✅ Python يعمل بشكل صحيح!
✅ إصدار Python: {sys.version.split()[0]}
✅ Tkinter متوفر ويعمل
✅ جميع الملفات موجودة

📁 الملفات المتوفرة:
"""
    
    # عد الملفات
    py_files = [f for f in os.listdir('.') if f.endswith('.py')]
    info_text += f"• {len(py_files)} ملف Python\n"
    
    bat_files = [f for f in os.listdir('.') if f.endswith('.bat')]
    info_text += f"• {len(bat_files)} ملف تشغيل\n"
    
    info_text += f"• المجلد: {os.getcwd()}\n"
    
    info_label = tk.Label(
        main_frame,
        text=info_text,
        font=('Arial', 11),
        fg='#212529',
        bg='#FFFFFF',
        justify=tk.LEFT
    )
    info_label.pack(pady=20)
    
    # أزرار التشغيل
    buttons_frame = tk.Frame(main_frame, bg='#FFFFFF')
    buttons_frame.pack(pady=30)
    
    def run_demo():
        """تشغيل النسخة التجريبية"""
        try:
            root.destroy()
            import demo_app
            app = demo_app.LabDemoApp()
            app.run()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تشغيل النسخة التجريبية:\n{str(e)}")
    
    def run_full():
        """تشغيل التطبيق الكامل"""
        try:
            root.destroy()
            import main
            app = main.LabManagementSystem()
            app.run()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تشغيل التطبيق الكامل:\n{str(e)}\n\nجرب النسخة التجريبية أولاً")
    
    def show_files():
        """عرض قائمة الملفات"""
        files_window = tk.Toplevel(root)
        files_window.title("ملفات التطبيق")
        files_window.geometry("500x400")
        files_window.configure(bg='#FFFFFF')
        
        text_widget = tk.Text(files_window, wrap=tk.WORD, font=('Arial', 10))
        scrollbar = tk.Scrollbar(files_window, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=10)
        
        # إدراج قائمة الملفات
        all_files = sorted(os.listdir('.'))
        for file in all_files:
            if os.path.isfile(file):
                size = os.path.getsize(file)
                text_widget.insert(tk.END, f"📄 {file} ({size} bytes)\n")
            else:
                text_widget.insert(tk.END, f"📁 {file}/\n")
        
        text_widget.configure(state='disabled')
    
    # الأزرار
    tk.Button(
        buttons_frame,
        text="🚀 تشغيل النسخة التجريبية",
        command=run_demo,
        bg='#28A745',
        fg='white',
        font=('Arial', 12, 'bold'),
        width=25,
        height=2
    ).pack(pady=10)
    
    tk.Button(
        buttons_frame,
        text="⚡ تشغيل التطبيق الكامل",
        command=run_full,
        bg='#2E86AB',
        fg='white',
        font=('Arial', 12, 'bold'),
        width=25,
        height=2
    ).pack(pady=10)
    
    tk.Button(
        buttons_frame,
        text="📁 عرض الملفات",
        command=show_files,
        bg='#FFC107',
        fg='black',
        font=('Arial', 12, 'bold'),
        width=25,
        height=2
    ).pack(pady=10)
    
    # زر الإغلاق
    tk.Button(
        main_frame,
        text="❌ إغلاق",
        command=root.quit,
        bg='#DC3545',
        fg='white',
        font=('Arial', 12, 'bold'),
        width=15,
        height=2
    ).pack(side=tk.BOTTOM, pady=20)
    
    # رسالة ترحيب
    welcome_label = tk.Label(
        main_frame,
        text="مرحباً بك في تطبيق مختبر الصحة العامة المركزي! 👋",
        font=('Arial', 12),
        fg='#6C757D',
        bg='#FFFFFF'
    )
    welcome_label.pack(side=tk.BOTTOM, pady=10)
    
    root.mainloop()

if __name__ == "__main__":
    print("========================================")
    print("  مختبر الصحة العامة المركزي - ذي قار")
    print("  تشغيل مباشر للتطبيق")
    print("========================================")
    print()
    print("✅ Python يعمل!")
    print("✅ تشغيل واجهة المستخدم...")
    print()
    
    try:
        main()
    except Exception as e:
        print(f"❌ خطأ: {e}")
        input("اضغط Enter للخروج...")
